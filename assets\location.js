class MapLocation extends HTMLElement {
  constructor() {
    super();
    this.initializeProperties();
    this.mapLoader = new MapLoader(this.querySelector('.map-box-section'));
  }

  initializeProperties() {
    this.specialOfferingHealthPass = ['Preventative Body Scan', 'Healthpass', 'Health Screen'];
    this.isMobile = window.matchMedia('(max-width: 768px)').matches;
    this.geolocationErrorMessage = window.utilsString.locationsString.errorMessage;
    this.locationString = window.utilsString.locationsString;

    // Convert dataset value to a proper boolean
    this.isEnableRadiusFilter = this.dataset.enableRadiusFilter === 'true';
    this.isEnabledQueryParams = this.dataset.enableUpdateQuearyParams === 'true';

    // Check if "Apply Now" button exists to determine if we should use delayed mobile filtering
    this.hasApplyButton = !!document.getElementById('applyAllFilters');
    this.shouldUseDelayedMobileFiltering = this.isMobile && this.hasApplyButton;

    // Default values for locations
    this.allLocations = [];
    this.mapMarkers = [];
    this.filteredLocations = [];
    this.markerClusterer = null;
    this.mapBound = null;
    this.infoWindow;
    this.map;
    this.defaultRadius = this.isEnableRadiusFilter ? Number(this.dataset.defaultRadius) : Infinity;
    this.defaultBusinessType = null;

    if (this.isEnabledQueryParams) {
      this.selectedFilterCount = 0;
      this.nearByMeParams = 'near_by_me';
      this.businessTypeParams = 'business_type';
      this.radiusParams = 'radius';
      this.searchParams = 'search';
      this.latParams = 'lat';
      this.lngParams = 'lng';
    }

    /**
     * Set default location coordinates using provided dataset values
     * Fallback to Los Angeles (lat: 34.052235, lng: -118.243683) if dataset values are undefined or invalid.
     */
    this.defaultLocationCoordinates = {
      lat: Number(this.dataset.defaultLat) || 34.052235,
      lng: Number(this.dataset.defaultLng) || -118.243683,
    };

    this.isCenterChangeRestricted = false;
    this.locakedCenterPoint = null;

    // Selected values for locations
    this.selectedNearByMe = null;
    this.selectedInputSearch = null;
    this.selectedLocationCoordinates = this.defaultLocationCoordinates;
    this.selectedRadius = this.defaultRadius;
    this.selectedBusinessType = this.defaultBusinessType;
    this.googleMapId = this.dataset.googleMapId;

    this.zoomLevel = this.isMobile ? Number(this.dataset.defaultZoomMobile) : Number(this.dataset.defaultZoom) || 10;
    this.maxZoomLevel = this.isMobile
      ? Number(this.dataset.defaultMaxZoomMobile)
      : Number(this.dataset.defaultMaxZoom) || 10;

    this.markerCenterMapZoomLevel = this.isMobile
      ? Number(this.dataset.markerCenterMapZoomLevel)
      : Number(this.dataset.markerCenterMapZoomLevelMobile) || 10;
  }

  setFilterLoading(isLoading) {
    const filterWrapper = this.querySelector('.filter-wrapper');

    if (filterWrapper) {
      filterWrapper.dataset.loading = isLoading.toString();
    }
  }

  /**
   * Lifecycle method that runs when the component is added to the DOM.
   * - Initializes the map location.
   * - Binds UI events for user interactions.
   * - Parses URL query parameters if enabled.
   */
  async connectedCallback() {
    this.setFilterLoading(true); // Set loading state before initialization
    await this.initializeMapLocation();
    this.bindUIEvents();

    if (this.isEnabledQueryParams) {
      this.parseSearchParams();
    }

    // Listen for custom 'filterDropdown:change' event and bind the handler to current context
    document.addEventListener('filterDropdown:change', this.onFilterDropdownChange.bind(this));
  }

  /**
   * Cleanup method to remove event listeners and prevent memory leaks
   */
  disconnectedCallback() {
    // Remove the idle event listener if it exists
    if (this.idleListener) {
      google.maps.event.removeListener(this.idleListener);
      this.idleListener = null;
    }

    // Clear any existing timeouts
    if (this.debouncedIdleHandler) {
      this.debouncedIdleHandler = null;
    }
  }

  /**
   * Event handler for dropdown filter changes.
   * Responds to 'radiusSelector' and 'businessTypeSelector' changes.
   * Updates the selected filter values and triggers relevant map update logic.
   * On mobile, filters are stored but not applied until "Apply Now" is clicked.
   *
   * @param {CustomEvent} e - Custom event containing dropdown ID and selected value.
   */
  onFilterDropdownChange(e) {
    const { id, value } = e.detail;
    const highlightSelectedOption = this.highlightSelectedOption(value);

    switch (id) {
      case 'radiusSelector':
        this.selectedRadius = value;
        this.handleRadiusSelection(highlightSelectedOption);
        break;
      case 'businessTypeSelector':
        this.selectedBusinessType = value;
        this.handleBusinessTypeFilterSelection(highlightSelectedOption);
        break;
      default:
        break;
    }

    // Apply filters immediately on desktop or home page. On mobile location page, wait for "Apply Now"
    if (!this.shouldUseDelayedMobileFiltering && this.mapBound) {
      this.handleMapChangeEvent(this.mapBound);
    }
  }

  /**
   * Initializes the map location by loading the Google Maps API,
   * fetching location data, and rendering the map.
   */
  async initializeMapLocation() {
    const { googleApiKey, locationsApi } = this.dataset;

    if (!googleApiKey || !locationsApi) {
      this.mapLoader.hideLoader();
      this.setFilterLoading(false);
      return;
    }

    try {
      this.mapLoader.showLoader(); // Show loader while the map is loading
      await this.checkGeolocationPermission(); // Check for geolocation permission on page load

      // Load Google Maps API with the required libraries
      await GoogleMapsLoader.loadGoogleMapsAPI({
        key: googleApiKey,
        libraries: 'maps,geometry,marker',
        markerClusterer: true,
      });

      // Fetch location data from the API
      const locations = await LocationApiService.fetchLocations(locationsApi);
      if (!locations) {
        throw new Error('Unable to fetch locations data');
      }

      const allowedCountries =
        this.dataset.filterCountries?.split(',').map((country) => country.trim().toLowerCase()) ?? [];

      this.allLocations = Array.isArray(locations)
        ? locations.filter((location) => {
            const displayLocationOnHealthPass = location.displayLocationOnHealthPass === true;
            const isInAllowedCountry =
              allowedCountries.length === 0 || allowedCountries.includes(location.country?.trim()?.toLowerCase());
            return displayLocationOnHealthPass && isInAllowedCountry;
          })
        : [];

      // Initialize the map inside the specified container
      await this.initializeMap(this.querySelector('.map-container'));

      // Manually trigger the initial map update instead of relying on idle event
      if (this.map && this.map.getBounds()) {
        this.handleMapChangeEvent(this.map.getBounds());
      }

      // Handle initial geolocation after map is initialized
      if (this.initialGeolocationPermission?.state === 'granted' && this.initialGeolocationPermission.enableNearMe) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            this.processGeolocation(position);
            this.toggleNearMeButton(true);
          },
          (error) => {
            this.displayGeolocationError(error);
            this.loadDefaultUSALocations();
          }
        );
      } else {
        this.loadDefaultUSALocations();
      }
    } catch (error) {
      console.error('Error initializing map location:', error);
    } finally {
      this.mapLoader.hideLoader();
      this.setFilterLoading(false);
    }
  }

  /**
   * Initializes the Google Map instance within the given container.
   * Configures various map settings such as zoom, controls, and map ID.
   * Otherwise, it filters and loads locations based on the selected coordinates.
   */
  async initializeMap(container) {
    this.mapOptions = {
      center: this.defaultLocationCoordinates, // Map center coordinates
      zoom: this.zoomLevel, // Initial zoom level
      zoomControl: true, // Show zoom controls
      draggable: true, // Enable map dragging
      mapTypeControl: false, // Hide map type switch
      streetViewControl: false, // Hide Street View
      fullscreenControl: false, // Hide fullscreen button
      minZoom: this.zoomLevel, // Minimum zoom level
      maxZoom: this.maxZoomLevel, // Maximum zoom level
      mapId: this.googleMapId, // Custom map style ID
      disableDefaultUI: true, // Disable all default UI controls
    };

    this.map = new google.maps.Map(container, this.mapOptions);

    // Create a debounced version of the idle handler
    this.debouncedIdleHandler = this.debounce(() => {
      this.mapBound = this.map.getBounds();

      if (this.mapBound) this.handleMapChangeEvent(this.mapBound);
    }, ON_CHANGE_DEBOUNCE_TIMER);

    this.idleListener = this.map.addListener('idle', this.debouncedIdleHandler);
  }

  /**
   * Creates a debounced version of a function.
   * @param {Function} func - The function to debounce
   * @param {number} wait - The number of milliseconds to delay
   * @returns {Function} - The debounced function
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func.apply(this, args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * Handles the map idle event, which is triggered when the map stops moving.
   * - Filters locations within the current map bounds.
   * - Updates the selected location coordinates based on the map center.
   * - Updates URL query parameters if enabled.
   * - Filters locations based on the selected criteria (radius, business type).
   * - Loads the filtered locations onto the map and sidebar.
   * @param {google.maps.LatLngBounds} bounds - The current map bounds
   * @param {boolean} forceUpdate - Force update even on mobile (used by Apply Now button)
   */
  handleMapChangeEvent(bounds, forceUpdate = false) {
    // On mobile with Apply button, only apply filters when explicitly forced (via Apply Now button)
    // On home page or desktop, apply filters immediately
    if (this.shouldUseDelayedMobileFiltering && !forceUpdate && this.hasActiveFilters()) {
      return; // Skip filter application on mobile location page unless forced
    }

    // Reset the radius to default if the map center is outside the locked center point
    if (this.locakedCenterPoint && bounds && !bounds.contains(this.locakedCenterPoint)) {
      this.selectedRadius = this.defaultRadius;
    }

    const filteredLocationsWithinBounds = this.filterLocationsWithinBounds(bounds);

    // Update the selected location coordinates only if the center is not restricted
    if (!this.isCenterChangeRestricted) {
      this.selectedLocationCoordinates = {
        lat: this.map.getCenter().lat(),
        lng: this.map.getCenter().lng(),
      };
    }

    // Update URL query parameters if enabled and if not using search input or 'Near Me' filter
    if (this.isEnabledQueryParams && (!this.enableSearchInput || !this.selectedNearByMe)) {
      this.updateURLQueryParams(
        this.selectedNearByMe,
        this.selectedInputSearch,
        this.selectedLocationCoordinates.lat,
        this.selectedLocationCoordinates.lng,
        this.selectedRadius,
        this.selectedBusinessType
      );
    }

    // Filter locations based on the selected criteria
    const newFilteredLocations = this.filterLocations(
      filteredLocationsWithinBounds,
      this.selectedLocationCoordinates.lat,
      this.selectedLocationCoordinates.lng,
      this.selectedRadius,
      this.selectedBusinessType
    );

    // Only load locations if they have actually changed
    if (this.locationsHaveChanged(newFilteredLocations)) {
      this.previousFilteredLocations = [...newFilteredLocations]; // Store copy for next comparison
      this.filteredLocations = newFilteredLocations;
      this.loadLocations(this.filteredLocations);
    }
  }

  // Filters locations within the current map bounds
  filterLocationsWithinBounds(bounds) {
    if (!bounds) return [];

    const filtered = this.allLocations.filter((location) => {
      const locationLatLng = new google.maps.LatLng(parseFloat(location.latitude), parseFloat(location.longitude));
      return bounds.contains(locationLatLng);
    });

    return filtered;
  }

  /**
   * Binds UI events based on dataset attributes.
   * - If "enableNearMe" is set to true, binds the "near me" button event.
   * - If "enableSearchInput" is set to true, binds the search input event.
   * - If "enableRadiusFilter" is set to true, binds the radius filter event.
   * - If "enableBusinessTypeSearch" is set to true, binds the business type search event.
   */
  bindUIEvents() {
    // Check if the "near me" functionality is enabled and bind its event
    if (this.dataset.enableNearMe == 'true') {
      this.nearMeButtonElem = this.querySelector('.near-me-button');
      this.bindNearMeEvent();
    }

    // Check if the search input functionality is enabled and bind its event
    if (this.dataset.enableSearchInput == 'true') {
      this.bindSearchInputEvent();
    }

    // Check if all filters are enabled and bind their events
    if (
      ['enableRadiusFilter', 'enableBusinessTypeFilter', 'enableSearchInput', 'enableNearMe'].every(
        (key) => this.dataset[key] === 'true'
      )
    ) {
      this.bindRadiusFilterEvent();
      this.bindBusinessTypeFilterEvent();
      this.bindApplyAllFiltersEvent();
      this.resetAllFilters();
      this.handleClose();
    }
  }

  /**
   * Binds the event for the "near me" button.
   * Adds an event listener that triggers the handleNearMeClick method when the button is clicked.
   */
  bindNearMeEvent() {
    this.nearMeButtonElem.addEventListener('click', () => this.handleNearMeClick());
  }

  /**
   * Binds the event for the search input.
   * Adds an event listener to handle user input and perform a search operation.
   */
  bindSearchInputEvent() {
    const handleInput = this.debounce((e) => {
      const query = e.target.value.trim();
      if (query.length >= 3 || query === '') {
        this.handleInputSearch(query);
      } else {
        this.hideAutocompleteSuggestions();
      }
    }, ON_CHANGE_DEBOUNCE_TIMER);

    const searchInput = this.querySelector('.search-input');
    if (!searchInput) return;

    searchInput.addEventListener('input', handleInput);

    // Add click outside listener to hide suggestions
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.input-search-block')) {
        this.hideAutocompleteSuggestions();
      }
    });

    // Add keyboard navigation
    searchInput.addEventListener('keydown', (e) => {
      const suggestions = this.querySelectorAll('#autocompleteSuggestions li');
      const currentIndex = Array.from(suggestions).findIndex((item) => item.classList.contains('focus-option-item'));

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          if (suggestions.length > 0) {
            let newIndex = currentIndex + 1;
            if (currentIndex === -1) newIndex = 0;
            if (newIndex < suggestions.length) {
              suggestions.forEach((item) => item.classList.remove('focus-option-item'));
              suggestions[newIndex].classList.add('focus-option-item');
              suggestions[newIndex].scrollIntoView({ block: 'nearest', behavior: 'smooth' });
            }
          }
          break;

        case 'ArrowUp':
          e.preventDefault();
          if (suggestions.length > 0 && currentIndex > 0) {
            const newIndex = currentIndex - 1;
            suggestions.forEach((item) => item.classList.remove('focus-option-item'));
            suggestions[newIndex].classList.add('focus-option-item');
            suggestions[newIndex].scrollIntoView({ block: 'nearest', behavior: 'smooth' });
          }
          break;

        case 'Enter':
          e.preventDefault();
          const selectedItem = this.querySelector('#autocompleteSuggestions li.focus-option-item');
          if (selectedItem) {
            selectedItem.click();
          } else if (suggestions.length > 0 && currentIndex < 0) {
            suggestions[0].classList.add('focus-option-item');
            suggestions[0].scrollIntoView({ block: 'nearest', behavior: 'smooth' });
            suggestions[0].click();
          }
          break;

        case 'Escape':
          e.preventDefault();
          this.hideAutocompleteSuggestions();
          break;
      }
    });
  }

  /**
   * Binds the event for the "radius search option".
   * Adds an event listener that triggers the handleRadiusFilterClick method when the radius filter changes.
   */
  bindRadiusFilterEvent() {
    const radiusOptions = this.querySelectorAll('.select-radius');
    if (!radiusOptions.length) return;

    radiusOptions.forEach((optionItem) =>
      optionItem.addEventListener('click', () => this.handleRadiusSelection(optionItem))
    );
  }

  /**
   * Handles the selection of a new radius from the dropdown.
   * - Updates the selected radius value.
   * - If query parameters are enabled, it updates the URL to reflect the new radius.
   * - On desktop: Adjusts the map zoom level immediately.
   * - On mobile: Stores the selection for later application via "Apply Now" button.
   *
   * @param {HTMLElement} optionItem - The selected radius dropdown option.
   */
  handleRadiusSelection(optionItem) {
    this.selectedRadius = parseInt(optionItem.dataset.value);

    // Update URL params immediately on desktop or home page. On mobile location page, wait for "Apply Now"
    if (!this.shouldUseDelayedMobileFiltering && this.isEnabledQueryParams) {
      this.updateURLQueryParams(
        this.selectedNearByMe,
        this.selectedInputSearch,
        this.selectedLocationCoordinates.lat,
        this.selectedLocationCoordinates.lng,
        this.selectedRadius,
        this.selectedBusinessType
      );
    }

    // Adjust map zoom immediately on desktop or home page. On mobile location page, wait for "Apply Now"
    if (!this.shouldUseDelayedMobileFiltering) {
      if (this.selectedRadius === this.defaultRadius) {
        this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.zoomLevel);
      } else if (this.map.getZoom() < this.markerCenterMapZoomLevel) {
        this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.zoomLevel + 2);
      }
    }
  }

  /**
   * Binds the event for the "Business type option".
   * Adds an event listener that triggers the handleBusinessTypeFilterClick method when the selection changes.
   */
  bindBusinessTypeFilterEvent() {
    const businessTypeOptions = document.querySelectorAll('.business-type-item');
    if (!businessTypeOptions.length) return;

    businessTypeOptions.forEach((optionItem) =>
      optionItem.addEventListener('click', () => this.handleBusinessTypeFilterSelection(optionItem))
    );
  }

  /**
   * Handles the selection of a business type filter from the dropdown.
   * - On desktop: Shows loader, updates URL params, and applies changes immediately.
   * - On mobile: Stores the selection for later application via "Apply Now" button.
   *
   * @param {HTMLElement} optionItem - The selected business type dropdown option.
   */
  handleBusinessTypeFilterSelection(optionItem) {
    if (!this.isMobile) this.mapLoader.showLoader();

    this.selectedBusinessType = optionItem.dataset.value;
    this.selectedBusinessType =
      this.selectedBusinessType === 'all_facilities' ? this.defaultBusinessType : this.selectedBusinessType;

    // Update URL params immediately on desktop or home page. On mobile location page, wait for "Apply Now"
    if (!this.shouldUseDelayedMobileFiltering && this.isEnabledQueryParams) {
      this.updateURLQueryParams(
        this.selectedNearByMe,
        this.selectedInputSearch,
        this.selectedLocationCoordinates.lat,
        this.selectedLocationCoordinates.lng,
        this.selectedRadius,
        this.selectedBusinessType
      );
    }

    if (!this.isMobile) setTimeout(() => this.mapLoader.hideLoader(), 300);
  }

  /**
   * Updates the URL query parameters based on the provided filter values.
   *
   * This function modifies the browser's URL without reloading the page, ensuring that
   * search and filter parameters are reflected in the URL. If a parameter is provided,
   * it is added or updated in the query string; if it is null or undefined, it is removed.
   *
   * @param {boolean} nearByMe - Indicates if the "Near Me" filter is applied.
   * @param {string} search - The search term entered by the user.
   * @param {number} lat - latitude value for location-based filtering.
   * @param {number} lng - longitude value for location-based filtering.
   * @param {number} radius - Search radius for filtering results.
   * @param {string} businessType - The type of business being searched for.
   * @param {boolean} IsApplyFilters - Whether filters should be applied on mobile devices.
   */
  updateURLQueryParams(nearByMe, search, lat, lng, radius, businessType, IsApplyFilters) {
    if (this.isMobile && !IsApplyFilters) return;

    const params = new URLSearchParams(window.location.search);

    if (nearByMe != null) {
      params.set(this.nearByMeParams, nearByMe);
    } else {
      params.delete(this.nearByMeParams);
    }

    if (search) {
      params.set(this.searchParams, search);
    } else {
      params.delete(this.searchParams);
    }

    if (lat) {
      params.set(this.latParams, lat);
    } else {
      params.delete(this.latParams);
    }

    if (lng) {
      params.set(this.lngParams, lng);
    } else {
      params.delete(this.lngParams);
    }

    if (radius) {
      params.set(this.radiusParams, radius);
    } else {
      params.delete(this.radiusParams);
    }

    if (businessType) {
      params.set(this.businessTypeParams, businessType);
    } else {
      params.delete(this.businessTypeParams);
    }

    window.history.replaceState({}, '', `${window.location.pathname}?${params}`);

    this.updateUIElement();
  }

  /**
   * Updates the UI elements to reflect the currently selected filters.
   *
   * This method modifies UI components based on the applied search, location,
   * business type, and radius filters. It ensures that the displayed values match
   * the selected parameters and visually updates the elements accordingly.
   */
  updateUIElement() {
    this.selectedFilterCount = 0;
    const searchElement = this.querySelector('.search-input');
    const nearMeButton = this.querySelector('.near-me-button');

    if (searchElement) {
      searchElement.value = this.selectedInputSearch;
    }

    if (this.selectedInputSearch) {
      nearMeButton.classList.remove('selected');
      this.selectedFilterCount++;
    } else if (this.selectedNearByMe) {
      nearMeButton.classList.add('selected');
      this.selectedFilterCount++;
    } else {
      nearMeButton.classList.remove('selected');
    }

    const businessTypeElement = document.getElementById('businessTypeSelectorSelectedLabel');
    if (this.selectedBusinessType) {
      businessTypeElement.innerText = this.selectedBusinessType;

      this.highlightSelectedOption(this.selectedBusinessType);

      businessTypeElement.classList.replace('text-gray-5', 'text-gray-8');
      this.selectedFilterCount++;
    } else {
      businessTypeElement.innerText = this.locationString.allFacilityType;
      businessTypeElement.classList.replace('text-gray-8', 'text-gray-5');
    }

    const radiusElement = document.getElementById('radiusSelectorSelectedLabel');
    if (this.selectedRadius > 0) {
      radiusElement.innerText = `${this.selectedRadius} miles`;

      this.highlightSelectedOption(this.selectedRadius);

      radiusElement.classList.replace('text-gray-5', 'text-gray-8');
      this.selectedFilterCount++;
    } else {
      radiusElement.innerText = this.locationString.searchRadius;
      radiusElement.classList.replace('text-gray-8', 'text-gray-5');
    }
  }

  /**
   * Highlights the selected option in the UI.
   * @param {string | number} targetAttributeValue - The value of the `data-value` attribute to match against a list item.
   */
  highlightSelectedOption(targetAttributeValue) {
    const targetedListItem = document.querySelector(`[data-value="${targetAttributeValue}"]`);
    targetedListItem.classList.add('selected-option');

    return targetedListItem;
  }

  /**
   * Binds an event listener to the "Apply Filters" button to update filters and refresh the UI.
   * On mobile, this is when filters are actually applied to the map.
   * On desktop, this provides a way to manually apply filters if needed.
   */
  bindApplyAllFiltersEvent() {
    const applyFiltersButton = document.getElementById('applyAllFilters');
    if (!applyFiltersButton) return; // Skip if button doesn't exist (e.g., on home page)

    applyFiltersButton.addEventListener('click', () => {
      const dismissModal = document.querySelector('[data-dismiss="modal-selector"]');

      // Show loader while applying filters
      if (this.isMobile) this.mapLoader.showLoader();

      // Update URL search query parameters
      if (this.isEnabledQueryParams) {
        this.updateURLQueryParams(
          this.selectedNearByMe,
          this.selectedInputSearch,
          this.selectedLocationCoordinates.lat,
          this.selectedLocationCoordinates.lng,
          this.selectedRadius,
          this.selectedBusinessType,
          true
        );
      }

      // Apply map zoom changes for mobile (delayed from filter selection, Near Me, or search)
      if (this.isMobile) {
        // Handle Near Me location centering
        if (this.selectedNearByMe && this.selectedLocationCoordinates) {
          this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.markerCenterMapZoomLevel);
        }
        // Handle search input location centering
        else if (this.selectedInputSearch && this.selectedLocationCoordinates) {
          const zoomLevel = this.isCenterChangeRestricted ? 12 : this.zoomLevel;
          this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, zoomLevel);
        }
        // Handle radius-based centering
        else if (this.selectedRadius === this.defaultRadius) {
          this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.zoomLevel);
        } else if (this.map.getZoom() < this.markerCenterMapZoomLevel) {
          this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.zoomLevel + 2);
        }
      }

      // Apply filters to map (force update on mobile)
      if (this.mapBound) this.handleMapChangeEvent(this.mapBound, true);

      this.updateFilterCount(this.selectedFilterCount);

      // Hide loader after a short delay
      if (this.isMobile) setTimeout(() => this.mapLoader.hideLoader(), 500);

      if (dismissModal) {
        dismissModal.click();
      }
    });
  }

  /**
   * Updates the UI to reflect the current number of active filters.
   *
   * This method modifies the filter icon and displays the active filter count
   * when filters are applied. If no filters are active, the count is hidden.
   */
  updateFilterCount(numParams) {
    const activeFilterCountButton = document.querySelector('.active-filter-block');
    const filterIconButton = document.getElementById('filterIcon');

    if (numParams > 0) {
      filterIconButton.classList.add('selected');
      filterIconButton.classList.replace('justify-center', 'justify-between');
    }

    if (activeFilterCountButton) {
      const filterCount = activeFilterCountButton.querySelector('.filter-count');
      filterCount.textContent = numParams;
      activeFilterCountButton.style.display = numParams > 0 ? 'inline-block' : 'none';
    }
  }

  /**
   * Parses URL search parameters and updates the filter state accordingly.
   *
   * This method extracts query parameters from the URL and updates the filter values
   * such as location, search input, radius, and business type. It also ensures
   * that filters are applied correctly and updates the UI.
   */
  parseSearchParams() {
    const params = new URLSearchParams(window.location.search);

    this.selectedNearByMe = params.get(this.nearByMeParams) === 'true' || null;

    this.selectedInputSearch = params.get(this.searchParams);

    const latitude = params.get(this.latParams);
    const longitude = params.get(this.lngParams);
    if (latitude && longitude) {
      this.selectedLocationCoordinates = { lat: parseFloat(latitude), lng: parseFloat(longitude) };
      this.locakedCenterPoint = { lat: parseFloat(latitude), lng: parseFloat(longitude) };
    } else {
      // Fallback to default coordinates if not provided
      this.selectedLocationCoordinates = this.defaultLocationCoordinates;
    }

    const radiusString = params.get(this.radiusParams);
    if (radiusString) {
      this.selectedRadius = parseInt(radiusString);
    } else {
      this.selectedRadius = this.defaultRadius; // Fallback to default radius if not provided
    }

    this.selectedBusinessType = params.get(this.businessTypeParams);

    if (this.isEnabledQueryParams) {
      this.updateURLQueryParams(
        this.selectedNearByMe,
        this.selectedInputSearch,
        this.selectedLocationCoordinates.lat,
        this.selectedLocationCoordinates.lng,
        this.selectedRadius,
        this.selectedBusinessType,
        true
      );
    }

    // Update filters count
    this.updateFilterCount(this.selectedFilterCount);
  }

  /**
   * Displays a list of locations in the sidebar.
   * Clears existing content, creates a wrapper for location elements, and populates it based on the available locations.
   * If no locations are found, it displays a "no locations" message. Otherwise, it sorts the locations and adds them to the sidebar.
   * @param {Array} locations - The list of locations to display.
   * @param {string} selectedLocationId - The ID of the location that should remain selected
   */

  // TODO: We need to optimize this by adding the virtualized list the the list it will improve the performance
  displayLocations(locations, selectedLocationId) {
    const sidebar = document.getElementById('sidebar');

    // Remove existing event listener before clearing innerHTML
    sidebar.removeEventListener('click', this.sidebarClickHandler);

    // Remove existing sidebar list element
    sidebar.innerHTML = '';

    // Retrieve the value of from the sidebar dataset and convert it to a boolean
    // TODO: Add the condition for the healthpass subsciption
    const hasPayPerScanBundle = sidebar.dataset?.hasScanBundle === 'true';

    const locationWrapper = this.createLocationWrapper();
    const gridWrapper = this.createGridWrapper();

    if (locations.length === 0) {
      // Show "no locations" message if no locations are available
      gridWrapper.appendChild(this.createNoLocationElement());
    } else {
      // Sort locations before displaying them
      const sortedLocations = this.sortLocations(locations);

      sortedLocations.forEach((location) => {
        const locationElement = this.createLocationElement(location, hasPayPerScanBundle);

        // Show the previus selected location first into the list
        if (selectedLocationId && location.userId.toString() === selectedLocationId) {
          this.updateSidebarSelection(location, locationElement);
        }

        gridWrapper.appendChild(locationElement);
      });
    }

    locationWrapper.appendChild(gridWrapper);
    sidebar.appendChild(locationWrapper);

    // Create a bound event handler that we can remove later
    this.sidebarClickHandler = (event) => {
      let target = event.target.closest('.sidebar-list');
      if (!target) return;

      // Handle business hours click
      if (event.target.closest('.business-hours-block')) {
        event.stopPropagation();

        const locationId = target.dataset.locationId;
        const location = locations.find((loc) => loc.userId === Number(locationId));
        this.businessHours(location);
        document.body.style.overflowY = 'hidden';
        return;
      }

      // Handle the get-directions-link
      const phone = event.target.closest('.phone');
      const email = event.target.closest('.email');
      if (phone || email) {
        event.stopPropagation();
        return;
      }

      let getDirectionsLink = event.target.closest('.address-link');
      if (getDirectionsLink) {
        event.stopPropagation();

        this.getDirections(getDirectionsLink.dataset.getDirections);
        return;
      }

      // Handle the schedule modal button
      const scheduleScanButton = event.target.closest('.schedule-scan-modal-selector');
      if (scheduleScanButton) {
        event.stopPropagation();

        this.handleScheduleScanButtonClick(scheduleScanButton);
        return;
      }

      const purchaseScanButton = event.target.closest('.book-scan button.purchase-scan-button');
      if (purchaseScanButton) {
        event.stopPropagation();

        // Store the location ID in local storage
        setLocalStorageItem(LOCAL_STORAGE_KEY.LOCATION_ID, Number(target.dataset.locationId));
        window.location.href = purchaseScanButton.dataset.href;
        return;
      }

      // Normal location selection/deselection logic
      const locationId = target.dataset.locationId;
      const location = locations.find((loc) => loc.userId === Number(locationId));

      this.handleLocationClick(location, target);
    };

    // Attach event listener to the sidebar for event delegation
    sidebar.addEventListener('click', this.sidebarClickHandler);
  }

  /**
   * Displays the provided location markers on the map.
   * Optimized to only update changed markers instead of clearing all markers.
   * @param {Array} locations - Array of location objects to be displayed as markers on the map.
   */
  placeMarkers(locations) {
    // Skip update if markers are already correct
    if (!this.markersNeedUpdate(locations)) {
      return this.mapMarkers;
    }

    // Get current map bounds to optimize marker visibility
    const bounds = this.map ? this.map.getBounds() : null;

    // Create a set of location IDs for quick lookup
    const newLocationIds = new Set(locations.map((loc) => loc.userId));

    // Remove markers that are no longer needed or outside bounds
    this.mapMarkers = this.mapMarkers.filter((marker) => {
      const shouldKeep = marker.locationId && newLocationIds.has(marker.locationId);

      // Also check if marker is within bounds (optional optimization)
      const withinBounds = !bounds || bounds.contains(marker.position);

      if (!shouldKeep || !withinBounds) {
        marker.setMap(null);
        return false;
      }
      return true;
    });

    // Get existing marker location IDs
    const existingLocationIds = new Set(this.mapMarkers.map((marker) => marker.locationId));

    // Add new markers for locations that don't already have markers
    locations.forEach((location) => {
      if (!existingLocationIds.has(location.userId)) {
        const marker = this.createMarker(this.map, location);
        marker.locationId = location.userId; // Store location ID for tracking
        this.mapMarkers.push(marker);

        // Add a click listener to handle marker interactions
        marker.addListener('gmp-click', () => {
          this.handleMarkerClick(marker, location);
        });
      }
    });

    return this.mapMarkers;
  }

  /**
   * Creates a new Google Maps marker for the given location.
   *
   * @param {Object} location - The location data for the marker.
   * @returns {google.maps.Marker} - The created marker instance.
   */
  createMarker(map, location) {
    const mapMarker = document.createElement('div');
    const img = document.createElement('img');
    img.setAttribute('loading', 'lazy');
    img.setAttribute('alt', location.companyName);
    img.setAttribute('aria-label', location.companyName);
    img.setAttribute('class', 'marker-icon');
    img.src = location.isHealthPassPartner
      ? 'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/marker-primary-gradient.svg'
      : 'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/secondary-marker.svg';
    mapMarker.appendChild(img);

    const marker = new google.maps.marker.AdvancedMarkerElement({
      position: { lat: parseFloat(location.latitude), lng: parseFloat(location.longitude) },
      map: map,
      zIndex: location.isHealthPassPartner ? 1 : 0,
      title: location.companyName,
      content: mapMarker,
    });

    // Add isSelected property to track selected state
    marker.isSelected = false;

    return marker;
  }

  // Creates and returns a wrapper element for the sidebar list container
  createLocationWrapper() {
    const wrapper = document.createElement('div');
    wrapper.classList.add(
      'sidebar-list-container',
      'flex-none',
      'w-full',
      'md:max-w-[354px]',
      'md:w-[354px]',
      'md:pr-1',
      'md:max-h-[600px]',
      'md:overflow-y-auto',
      'scrollbar'
    );
    return wrapper;
  }

  // This grid layout organizes location items in a single-column format with spacing
  createGridWrapper() {
    const wrapper = document.createElement('div');
    wrapper.classList.add('grid', 'grid-cols-1', 'gap-2');
    return wrapper;
  }

  /**
   * Creates an element to display when no locations are found.
   * The element includes an image and a message indicating that no search results were found.
   * @returns {HTMLElement} - A DOM element representing the "no locations found" message.
   */
  createNoLocationElement() {
    const element = document.createElement('div');
    element.classList.add('sidebar-list', 'rounded-lg', 'border', 'border-gray-2', 'p-4');
    element.innerHTML = `
      <div class="flex flex-col gap-4 justify-center items-center">
        <div class="image-icon">
          <img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/search-not-found.png?v=1708408600" loading="lazy">
        </div>
        <div class="text-center">
          <p class="text-base font-semibold text-secondary">${this.locationString.noSearchResult.title}</p>
          <p class="text-sm font-normal text-gray-8">${this.locationString.noSearchResult.message}</p>
        </div>   
      </div>
    `;
    return element;
  }

  /**
   * Creates a location element to be displayed in the sidebar.
   * @param {Object} location - The location data to be displayed.
   * @param {boolean} hasPayPerScanBundle - Whether the Pay-Per-Scan bundle feature is enabled.
   * @returns {HTMLElement} - A DOM element representing the location.
   */
  createLocationElement(location, hasPayPerScanBundle) {
    const element = document.createElement('div');
    element.classList.add('sidebar-list', 'rounded-lg', 'cursor-pointer', 'border', 'border-gray-2', 'p-4', 'relative');
    element.setAttribute('data-location-id', `${location.userId}`);
    element.setAttribute('data-location-title', `${location.companyName}`);

    // Populate the location element with relevant details
    element.innerHTML = `<div class="space-y-4 grid">
        <div class="text-block ${location.isHealthPassPartner ? 'w-[92%]' : 'w-full'}">
          ${this.renderHealthPassInfo(location)}
          <h2 class="text-base font-bold text-secondary mb-2">${location.companyName}</h2>
          ${this.renderLocationInfo(location)}
        </div>     
        ${this.renderBusinessTypeBlock(location)}
        ${this.renderBusinessHoursBlock(location)}
        ${this.renderAdditionalInfo(location)}
        ${this.renderScanButton(location, hasPayPerScanBundle)}
        <div id="${location.userId}" class="display-map-for-all-locations hidden rounded-lg"></div>
      </div>`;

    return element;
  }

  /**
   * Loads and displays location markers on the map.
   * @param {Array} locations - Array of location objects to be displayed as markers on the map.
   */
  loadLocations(locations) {
    const selectedLocationId = this.getSelectedLocationId();
    const selectedMarkerPosition = this.getSelectedMarkerPosition();
    const selectedLocation = this.findLocationById(locations, selectedLocationId);

    // Move the selected location to the top of the list
    locations = this.prioritizeSelectedLocation(locations, selectedLocation);

    // Update sidebar list if enabled via dataset
    if (this.shouldDisplaySidebar()) {
      this.displayLocations(locations, selectedLocationId);
      this.scrollToSelectedLocation(selectedLocationId);
    }

    const markers = this.placeMarkers(locations); // Place optimized markers on the map
    this.initMarkerClusterer(markers); // Initialize/update clustering for markers

    // Re-select previously selected marker/location if applicable
    this.restoreSelectedMarker(selectedLocation, selectedMarkerPosition, locations);
  }

  // Retrieves the ID of the currently selected location in the sidebar list.
  getSelectedLocationId() {
    const selectedItem = this.querySelector('.sidebar-list.selected, .sidebar-list.selected-secondary');
    return selectedItem?.dataset.locationId || null;
  }

  // Gets the coordinates of the currently selected marker from the map.
  getSelectedMarkerPosition() {
    const selectedMarker = this.mapMarkers.find((marker) => marker.isSelected);
    return selectedMarker ? { lat: selectedMarker.position.lat, lng: selectedMarker.position.lng } : null;
  }

  // Finds and returns the location object in the list matching the given ID.
  findLocationById(locations, id) {
    return id ? locations.find((loc) => loc.userId.toString() === id) : null;
  }

  // Moves the selected location to the beginning of the locations array.
  prioritizeSelectedLocation(locations, selectedLocation) {
    if (!selectedLocation) return locations;
    return [
      selectedLocation,
      ...locations.filter((loc) => loc.userId.toString() !== selectedLocation.userId.toString()),
    ];
  }

  // Checks if the sidebar location list is enabled via data attribute.
  shouldDisplaySidebar() {
    return this.dataset.sidebarLocationList === 'true';
  }

  // Scrolls the sidebar so the selected location is visible to the user.
  scrollToSelectedLocation(locationId) {
    const selectedElement = this.querySelector(`[data-location-id="${locationId}"]`);
    const container = this.querySelector('.sidebar-list-container');
    if (selectedElement && container) {
      container.scrollTo({
        top: selectedElement.offsetTop - container.offsetTop,
        behavior: 'auto',
      });
    }
  }

  // Initializes or updates a MarkerClusterer with custom rendering and clustering logic.
  initMarkerClusterer(markers) {
    // If clusterer already exists, update it with new markers instead of recreating
    if (this.markerClusterer) {
      this.markerClusterer.clearMarkers();
      this.markerClusterer.addMarkers(markers);
    } else {
      // Create new clusterer
      this.markerClusterer = new markerClusterer.MarkerClusterer({
        markers: markers,
        map: this.map,
        algorithm: new markerClusterer.GridAlgorithm({ gridSize: 38, maxZoom: 13 }),
        renderer: {
          render: ({ count, position, markers, map }) => {
            const clusterElement = document.createElement('div');
            clusterElement.className = 'custom-cluster';
            clusterElement.innerHTML = `<div class="cluster-content"><span class="cluster-count">${count}</span></div>`;
            clusterElement.addEventListener('click', () => {
              const bounds = new google.maps.LatLngBounds();
              markers.forEach((marker) => bounds.extend(marker.position));
              map.fitBounds(bounds, { padding: { top: 18, right: 18, bottom: 18, left: 18 } });
            });
            return new google.maps.marker.AdvancedMarkerElement({
              position,
              content: clusterElement,
            });
          },
        },
      });
    }
  }

  // Attempts to re-select a marker and open its info window after refreshing the map.
  restoreSelectedMarker(selectedLocation, selectedMarkerPosition, locations) {
    const markerToSelect = this.findMarkerToSelect(selectedLocation, selectedMarkerPosition);

    if (!markerToSelect) return;

    const locationToSelect = selectedLocation || this.findLocationByCoords(locations, markerToSelect.position);

    if (locationToSelect) {
      this.openInfoWindow(markerToSelect, locationToSelect, { disableAutoPan: true });
    }
  }

  // Finds a marker that matches either the selected location or previously saved coordinates.
  findMarkerToSelect(selectedLocation, markerPosition) {
    if (selectedLocation) {
      return this.mapMarkers.find(
        (marker) =>
          marker.position.lat === parseFloat(selectedLocation.latitude) &&
          marker.position.lng === parseFloat(selectedLocation.longitude)
      );
    }

    if (markerPosition) {
      return this.mapMarkers.find(
        (marker) => marker.position.lat === markerPosition.lat && marker.position.lng === markerPosition.lng
      );
    }

    return null;
  }

  // Finds the location object from a list that matches the given lat/lng coordinates.
  findLocationByCoords(locations, position) {
    return locations.find(
      (loc) => parseFloat(loc.latitude) === position.lat && parseFloat(loc.longitude) === position.lng
    );
  }

  /**
   * Renders the Health Pass Partner badge if the location is a partner.
   * This method returns an image and a label indicating the Health Pass status.
   */
  renderHealthPassInfo(location) {
    return location.isHealthPassPartner
      ? `<img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/health-pass.png?&width=24" loading="lazy" class="absolute rotate-y-animation w-6 h-auto right-2 top-2" /><h2 class="text-primary-gradient text-[11px] font-medium leading-[16px] uppercase">${this.locationString.healthPassPartner}</h2>`
      : '';
  }

  /**
   * Renders location information including address, phone, and email.
   * - The address is wrapped in a clickable div with a "Get Directions" feature.
   * - The phone and email are displayed inside a hidden block, revealed when needed.
   *
   * @param {Object} location - The location data object.
   * @returns {string} - The HTML markup for the location info.
   */
  renderLocationInfo(location) {
    return `
      <div role="button"
        data-get-directions='${encodeURIComponent(location.address)}' 
        class="address-link flex w-fit gap-1 items-start group mb-2">
      <img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Pin.svg?v=**********" loading="lazy" width="16" height="16"/>
      <p class="text-[12px] leading-[16px] text-gray-7 group-hover:text-primary">${location.address}</p>
      </div>
      <div class="phone-and-email-block space-y-2">
        <a href="tel:${location.phone}" class="phone flex w-fit gap-1 group">
          <img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Phone_d712d8df-2483-4d90-929c-34fc17112156.svg?v=**********" loading="lazy" width="16" height="16"/>
          <p class="text-[12px] leading-[16px] text-gray-7 group-hover:text-primary">${location.phone}</p>
        </a>
        <a href="mailto:${location.email}" class="email flex w-fit gap-1 group">
          <img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Mail_Closed.svg?v=**********" loading="lazy" width="16" height="16"/>
          <p class="text-[12px] leading-[16px] text-gray-7 group-hover:text-primary">${location.email}</p>
        </a>
      </div>
    `;
  }

  /**
   * Renders the business type block for a given location.
   * - Displays the business type as a styled badge.
   * - If the location is a Health Pass Partner, additional special offerings are displayed.
   *
   * @param {Object} location - The location data object.
   * @returns {string} - The HTML markup for the business type block.
   */
  renderBusinessTypeBlock(location) {
    if (!location.businessTypeName) return '';
    const healthPassOfferings = location.isHealthPassPartner
      ? this.specialOfferingHealthPass
          .map(
            (offer) =>
              `<div class="py-[2px] px-2 rounded-full text-[12px] leading-[16px] bg-[#fff6f8] text-black chips-primary-gradient-outline">${offer}</div>`
          )
          .join('')
      : '';
    return `<div class="business-type-block">
      <p class="text-[12px] leading-[16px] text-gray-7 pb-1">${this.locationString.businessOfferings}</p>
      <div class="select-none flex flex-wrap gap-1">
        ${healthPassOfferings}
        <div class="py-[2px] px-2 rounded-full text-[12px] leading-[16px] bg-[#EBEBEB] text-black">
          ${location.businessTypeName}
        </div>
      </div>
    </div>`;
  }

  /**
   * Renders the business hours block for a given location.
   * - Displays an interactive section with business hours information.
   * - If business hours data is missing, returns an empty string.
   *
   * @param {Object} location - The location data object.
   * @returns {string} - The HTML markup for the business hours block.
   */
  renderBusinessHoursBlock(location) {
    if (!location.businessHoursOfOperation) return '';
    return `
    <div role="button" class="business-hours-block flex justify-between py-4 border-y border-gray-2">
      <div class="text-block flex gap-2">
        <img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Clock-new.svg?v=**********" loading="lazy" />
        <p class="text-xs text-gray-7">${this.locationString.businessHours}</p>
      </div>
      <img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Arrow_Right.svg?v=1729581219" loading="lazy" />
    </div>
  `;
  }

  /**
   * Renders additional information about a location.
   * - Displays pricing for members and non-members.
   * - Shows appointment and walk-in requirements.
   * - Hides the section if no relevant data is available.
   *
   * @param {Object} location - The location data object.
   * @returns {string} - The HTML markup for the additional info block.
   */
  renderAdditionalInfo(location) {
    return `
    <div class="others-info-wrapper ${
      this.hasLocationOthersInfo(location) ? 'hidden !mt-0' : !location.businessHoursOfOperation ? 'hidden' : ''
    }">
      ${
        !location.isHealthPassPartner
          ? `
          ${this.createInfoBlock(
            'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/ATM_Dollar.svg?v=**********',
            this.locationString.memberScanPrice,
            this.formatValue(location.howMuchForMembers, true)
          )}
          ${this.createInfoBlock(
            'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/ATM_Dollar.svg?v=**********',
            this.locationString.nonMemberScanPrice,
            this.formatValue(location.howMuchForNonMembers, true)
          )}
          ${this.createInfoBlock(
            'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Credit_Card.svg?v=**********',
            this.locationString.walkInsWelcome,
            this.formatValue(location.doYouRequireAppointmentToScan)
          )}
          ${this.createInfoBlock(
            'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Calendar.svg?v=**********',
            this.locationString.appointmentToScanRequired,
            this.formatValue(location.doYouRequireAppointmentToScan)
          )}`
          : ''
      }  
    </div>`;
  }

  /**
   * Renders the scan button block for a given location.
   * - If the location is a Health Pass Partner:
   *   - If the user has a Pay-Per-Scan bundle, shows the "Book Scan" button.
   *   - Otherwise, displays the "Purchase Scan" button.
   * - If not a Health Pass Partner, shows the "Inquire Now" button.
   *
   * @param {Object} location - The location data object.
   * @returns {string} - The HTML markup for the scan button block.
   */
  renderScanButton(location, hasPayPerScanBundle) {
    return `<div class="button-block book-scan order-last">
      ${
        location.isHealthPassPartner
          ? hasPayPerScanBundle
            ? `${this.scheduleScanModalButton(location.userId, this.locationString.buttons.bookScan, true)}`
            : `${this.purchaseScanButton()}`
          : `${this.scheduleScanModalButton(location.userId, this.locationString.buttons.inquireNow)}`
      }
    </div>`;
  }

  /**
   * Generates a schedule scan button.
   * - The button style changes based on the `isSolid` flag.
   * - Clicking the button triggers a modal for scheduling a scan.
   *
   * @param {string} userId - The unique user ID associated with the scan.
   * @param {string} label - The button label text.
   * @param {boolean} [isSolid=false] - Determines if the button should have a solid or outlined style.
   * @returns {string} - The HTML markup for the schedule scan button.
   */
  scheduleScanModalButton(userId, label, isSolid = false) {
    return `<button role="button" 
      class="${
        isSolid ? 'button-primary-gradient' : 'button-primary-gradient-outline'
      } px-8 py-[6px] text-sm font-bold rounded-full w-full schedule-scan-modal-selector flex gap-2 justify-center" data-toggle="modal" data-target-userId="${userId}">
        <img src="${
          isSolid
            ? 'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/calendar-white.svg?v=1713961713'
            : 'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/calender-icon-primary-gradient.svg?v=1713961881'
        }" />
        <span class="${isSolid ? 'font-bold' : 'text-primary-gradient'}">${label}</span>
    </button>`;
  }

  /**
   * Generates a "Purchase Scan" button.
   * - This button redirects users to the pricing page.
   * - Uses a primary gradient button style for visibility.
   *
   * @returns {string} - The HTML markup for the purchase scan button.
   */
  purchaseScanButton() {
    return `<button role="button" 
      data-href="${PAGE_URLS.PRICING}" class="button-primary-gradient purchase-scan-button w-full flex gap-2 justify-center text-sm">
      <img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/calendar-white.svg?v=1713961713"/>
      <span class="font-bold">${this.locationString.buttons.purchaseScan}</span>
    </button>`;
  }

  /**
   * Creates an information block with an icon, label, and value.
   * - Displays only if `value` is provided.
   * - Uses a flex layout with a label on the left and a value on the right.
   *
   * @param {string} icon - The URL of the icon to be displayed.
   * @param {string} label - The descriptive label for the information.
   * @param {string} value - The actual data to display. If falsy, the block is not rendered.
   * @returns {string} - The HTML markup for the info block or an empty string if `value` is falsy.
   */
  createInfoBlock(icon, label, value) {
    return value
      ? `<div class="info-block flex justify-between py-4 border-b border-gray-2 cursor-default">
      <div class="text-block flex gap-2">
        <img src="${icon}" width="16" height="16"/>
        <p class="text-xs text-gray-7">${label}</p>
      </div>
      <p class="text-xs text-primary-gradient">${value}</p>
    </div>`
      : '';
  }

  /**
   * Generates and displays a modal containing business hours and scanner business hours.
   * - Parses business hours data from the location object.
   * - Creates a modal element dynamically and appends it to the document.
   * - Provides a toggle functionality for switching between business hours and scanner business hours.
   * - Implements a close button to remove the modal from the DOM.
   *
   * @param {Object} location - The location data containing business hours information.
   */
  businessHours(location) {
    const businessHoursData = location.businessHoursOfOperation ? JSON.parse(location.businessHoursOfOperation) : [];
    const scannerBusinessHoursData = location.scannerBusinessHoursOfOperation
      ? JSON.parse(location.scannerBusinessHoursOfOperation)
      : [];

    const businessHours = 'business-hours';
    const scannerBusinessHours = 'scanner-business-hours';

    const modalHTML = `<div class="schedule-scan-modal-overlay active" id="location-${location.userId}">
      <div class="schedule-scan-modal w-[90%] bg-white px-6 py-[30px] shadow-sm rounded-lg flex justify-center items-center lg:max-w-[462px] lg:w-[462px] active">
        <div class="modal-content w-full">
          <div class="space-y-6">
            <div class="modal-header flex gap-2">
              ${
                location.isHealthPassPartner
                  ? `<img src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/health-pass.png?&width=24" loading="lazy" class="rotate-y-animation w-5 h-auto" />`
                  : ''
              }
             <div class="w-full flex items-center justify-between">
                <div>
                  ${
                    location.isHealthPassPartner
                      ? `<h2 class="text-primary-gradient text-xs font-medium uppercase">${this.locationString.healthPassPartner}</h2>`
                      : ''
                  }
                  <h3 class="text-xl font-bold text-secondary">${location.companyName}</h3>
                </div>
                <button role="button" class="close-modal schedule-scan-modal-close cursor-pointer hover:scale-125">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none"><path d="M19 5.5L12 12.5M12 12.5L5 19.5M12 12.5L19 19.5M12 12.5L5 5.5" stroke="#7E7E7E" stroke-width="1.48492"/></svg>
                </button>
              </div>    
              </div>
            </div>
            <div class="modal-content-body">
              ${
                businessHoursData.length && scannerBusinessHoursData.length
                  ? `<div class="grid grid-cols-2 gap-2 rounded-lg bg-gray-1 p-1 my-6">
                    ${[businessHours, scannerBusinessHours]
                      .map(
                        (type) => `<div class="${type}-operation">
                          <input type="radio" name="option" id="${type}-${location.userId}" value="${type}-${
                          location.userId
                        }" class="peer hidden" ${type === businessHours ? 'checked' : ''} />
                          <label for="${type}-${
                          location.userId
                        }" class="text-sm text-gray-5 block cursor-pointer select-none font-bold rounded-md p-2 text-center peer-checked:bg-white peer-checked:text-primary">
                            ${
                              type === businessHours
                                ? this.locationString.businessHours
                                : this.locationString.scanningHours
                            }
                          </label>
                        </div>`
                      )
                      .join('')}
                  </div>`
                  : `<p class="text-lg text-secondary border-b font-bold border-gray-2 mb-4 pb-3">${this.locationString.businessHours}</p>`
              }
              <div class="timing-wrapper space-y-6">
                <div class="business-hours-container space-y-4">${this.renderBusinessHours(businessHoursData)}</div>
                <div class="scanner-business-hours-container hidden space-y-4">
                ${this.renderBusinessHours(scannerBusinessHoursData)}</div>
              </div> 
            </div>
          </div>
        </div>
      </div>
    </div>`;

    // Create a container to hold the modal
    const modalContainer = document.createElement('div');
    modalContainer.classList.add('modal-wrapper');
    modalContainer.innerHTML = modalHTML;
    document.body.appendChild(modalContainer);

    // Function to close the modal
    const closeModal = () => {
      modalContainer.querySelector('.schedule-scan-modal-overlay').classList.remove('active');
      document.body.style.overflowY = '';

      setTimeout(() => {
        modalContainer.remove();
      }, 500);
    };

    // Add event listeners to close buttons
    const closeButtons = modalContainer.querySelectorAll('.close-modal');
    closeButtons.forEach((button) => {
      button.addEventListener('click', closeModal);
    });

    // Add toggle functionality
    const businessHoursRadio = modalContainer.querySelector(`#business-hours-${location.userId}`);
    const scannerBusinessHoursRadio = modalContainer.querySelector(`#scanner-business-hours-${location.userId}`);
    const businessHoursContainer = modalContainer.querySelector('.business-hours-container');
    const scannerBusinessHoursContainer = modalContainer.querySelector('.scanner-business-hours-container');

    businessHoursRadio.addEventListener('change', () => {
      if (businessHoursRadio.checked) {
        businessHoursContainer.classList.remove('hidden');
        scannerBusinessHoursContainer.classList.add('hidden');
      }
    });

    scannerBusinessHoursRadio.addEventListener('change', () => {
      if (scannerBusinessHoursRadio.checked) {
        scannerBusinessHoursContainer.classList.remove('hidden');
        businessHoursContainer.classList.add('hidden');
      }
    });
  }

  /**
   * Generates HTML for displaying business hours in a structured format.
   * - Iterates through each day's business hours data.
   * - Handles special cases like "Closed" and "Open 24 Hours".
   * - Properly formats opening and closing times.
   *
   * @param {Array} businessHoursData - Array of objects containing daily business hours.
   * @returns {string} - The generated HTML for business hours display.
   */
  renderBusinessHours(businessHoursData) {
    return businessHoursData
      .map(
        (day) => `<div class="business-hours-timing grid grid-cols-2 gap-2">
          <div class="day"><p class="paragraph-text !font-bold">${day.day}</p></div>
          <div class="timing grid gap-0.5">
            ${
              day.isClosed
                ? `<p class="paragraph-text">${this.locationString.closed}</p>`
                : day.isOpen24
                ? `<p class="paragraph-text">${this.locationString.open24}</p>`
                : Array.isArray(day.businessHours) && day.businessHours.length > 0
                ? day.businessHours
                    .map(
                      (hours) => `
                      <p class="paragraph-text">
                        ${hours.openTime ? hours.openTime + 'am' : '-'} - 
                        ${hours.closeTime ? hours.closeTime + 'pm' : '-'}
                      </p>
                    `
                    )
                    .join('')
                : `<p class="paragraph-text">${this.locationString.notAvailable}</p>`
            }
          </div>
        </div>`
      )
      .join('');
  }

  /**
   * Formats a given value based on its type.
   * - Returns an empty string if the value is null or undefined.
   * - Converts boolean values to "Yes" or "No".
   * - Formats numerical values as price if `isPrice` is true.
   *
   * @param {*} value - The value to format.
   * @param {boolean} [isPrice=false] - Whether the value should be formatted as a price.
   * @returns {string} - The formatted value.
   */
  formatValue(value, isPrice = false) {
    if (value === null || value === undefined) {
      return '';
    } else if (value === true) {
      return 'Yes';
    } else if (value === false) {
      return 'No';
    } else if (isPrice && !isNaN(value)) {
      return `$${value}`;
    } else {
      return value;
    }
  }

  /**
   * Handles location click event.
   * - Highlights the corresponding marker on the map.
   * - Updates the sidebar selection.
   *
   * @param {Object} location - The location object containing latitude and longitude.
   * @param {HTMLElement} locationElement - The DOM element associated with the location.
   */
  handleLocationClick(location, locationElement) {
    if (locationElement.classList.contains('selected') || locationElement.classList.contains('selected-secondary')) {
      this.deselectLocation();
      return;
    }

    // Find and highlight the corresponding marker
    const marker = this.mapMarkers.find(
      (m) => m.position.lat === parseFloat(location.latitude) && m.position.lng === parseFloat(location.longitude)
    );

    if (marker) this.handleMarkerClick(marker, location, true);
  }

  /**
   * Updates the sidebar selection when a location is clicked.
   * - Highlights the clicked location.
   * - Shows/hides additional info like phone, email, and other details.
   *
   * @param {Object} location - The selected location object.
   * @param {HTMLElement} clickedElement - The clicked sidebar element.
   */
  updateSidebarSelection(location, clickedElement) {
    // Reset all sidebar items to remove selection styles and hide additional info
    document.querySelectorAll('.sidebar-list').forEach((item) => {
      item.classList.remove('selected', 'selected-secondary');
      item.querySelector('.others-info-wrapper')?.classList.add('hidden');
    });

    // Apply selection styles based on HealthPass partnership
    clickedElement.classList.add(location.isHealthPassPartner ? 'selected' : 'selected-secondary');

    // Show "others info" section if applicable
    const othersInfoWrapper = clickedElement.querySelector('.others-info-wrapper');
    const infoBlock = othersInfoWrapper?.querySelector('.info-block');

    if (!location.isHealthPassPartner && infoBlock) {
      othersInfoWrapper.classList.remove('hidden');
    }

    // Adjust styling if businessHoursOfOperation is missing
    if (!location.businessHoursOfOperation && othersInfoWrapper) {
      othersInfoWrapper.classList.remove('!mt-0');
      infoBlock?.classList.add('border-t');
    }
  }

  /**
   * Opens Google Maps with directions from the user's current location to the destination.
   *
   * @param {string} destination - The address or coordinates of the destination.
   */
  getDirections(destination) {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        function (position) {
          const currentLocation = position.coords.latitude + ',' + position.coords.longitude;
          const mapsUrl = `https://www.google.com/maps/dir/${currentLocation}/${destination}`;
          window.open(mapsUrl, '_blank');
        },
        function () {
          showToastMessage('Error getting current location. Please try again.');
        }
      );
    } else {
      showToastMessage('Geolocation is not supported by your browser.');
    }
  }

  /**
   * Checks if the location has additional information available.
   *
   * @param {Object} location - The location object containing various details.
   * @returns {boolean} - Returns true if any of the specified details are present, otherwise false.
   */
  hasLocationOthersInfo(location) {
    return (
      location.businessHoursOfOperation !== null ||
      location.howMuchForMembers !== null ||
      location.howMuchForNonMembers !== null ||
      location.doYouRequireAppointmentToScan !== null
    );
  }

  /**
   * Handles the click event for the schedule scan button.
   * Opens the schedule scan modal and sets up event listeners for closing it.
   *
   * @param {HTMLElement} scheduleScanButton - The button element that was clicked.
   */
  handleScheduleScanButtonClick(scheduleScanButton) {
    const userId = scheduleScanButton.getAttribute('data-target-userId');
    const modalOverlay = document.querySelector('.schedule-scan-modal-overlay');
    const modalContent = modalOverlay.querySelector('.schedule-scan-modal');
    const closeModal = document.querySelectorAll('.schedule-scan-modal-close');

    const successModal = document.getElementById('scheduleScanSuccessMessage');
    const formBlock = document.querySelector('.schedule-scan-form-block');
    const submitButton = document.getElementById('submitButton');
    document.querySelector('#scheduleScanForm input[name="userId"]').value = userId;

    modalOverlay.classList.add('active');
    modalContent.classList.add('active');
    document.body.classList.add('modal-open');

    closeModal.forEach((closeButton) => {
      closeButton.addEventListener('click', function () {
        modalOverlay.classList.remove('active');
        modalContent.classList.remove('active');
        formBlock.classList.remove('hidden');
        successModal.classList.add('hidden');
        document.body.classList.remove('modal-open');
        submitButton.disabled = true;
        scheduleScanForm.reset();
      });
    });
  }

  /**
   * Removes all existing markers from the map and clears the marker storage.
   * This helps in refreshing the markers when updating map data.
   * Use this only when you need to force clear all markers (e.g., reset filters).
   */
  clearMarkers() {
    if (this.markerClusterer) {
      this.markerClusterer?.clearMarkers();
      this.markerClusterer = null;
    }

    this.mapMarkers.forEach((marker) => marker.setMap(null));
    this.mapMarkers = [];
  }

  /**
   * Optimized marker clearing that only removes markers outside current bounds.
   * This is used by the optimized placeMarkers method.
   */
  clearMarkersOutsideBounds(bounds) {
    if (!bounds) return;

    this.mapMarkers = this.mapMarkers.filter((marker) => {
      const withinBounds = bounds.contains(marker.position);
      if (!withinBounds) {
        marker.setMap(null);
        return false;
      }
      return true;
    });
  }

  /**
   * Checks if the current markers match the provided locations.
   * Returns true if markers need updating, false if they're already correct.
   */
  markersNeedUpdate(locations) {
    // If different number of locations, definitely need update
    if (this.mapMarkers.length !== locations.length) {
      return true;
    }

    // Check if all location IDs match
    const currentLocationIds = new Set(this.mapMarkers.map((marker) => marker.locationId));
    const newLocationIds = new Set(locations.map((loc) => loc.userId));

    // If sets are different sizes or have different values, need update
    if (currentLocationIds.size !== newLocationIds.size) {
      return true;
    }

    for (let id of newLocationIds) {
      if (!currentLocationIds.has(id)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Checks if the filtered locations have changed compared to the previous set.
   * Returns true if locations have changed, false if they're the same.
   */
  locationsHaveChanged(newLocations) {
    // If no previous locations, definitely changed
    if (!this.previousFilteredLocations) {
      return true;
    }

    // If different number of locations, definitely changed
    if (this.previousFilteredLocations.length !== newLocations.length) {
      return true;
    }

    // Check if all location IDs match
    const previousLocationIds = new Set(this.previousFilteredLocations.map((loc) => loc.userId));
    const newLocationIds = new Set(newLocations.map((loc) => loc.userId));

    // If sets are different sizes or have different values, locations changed
    if (previousLocationIds.size !== newLocationIds.size) {
      return true;
    }

    for (let id of newLocationIds) {
      if (!previousLocationIds.has(id)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Checks if there are any active filters applied.
   * Returns true if any filters are active, false if all are at default values.
   */
  hasActiveFilters() {
    return (
      this.selectedNearByMe !== null ||
      this.selectedInputSearch !== null ||
      this.selectedRadius !== this.defaultRadius ||
      this.selectedBusinessType !== this.defaultBusinessType
    );
  }

  /**
   * Resets all mobile filter UI elements to their default state.
   */
  resetMobileFilterUI() {
    // Reset dropdown selections
    const radiusOptions = this.querySelectorAll('.select-radius');
    radiusOptions.forEach((option) => {
      option.classList.remove('selected');
      if (option.dataset.value === this.defaultRadius.toString()) {
        option.classList.add('selected');
      }
    });

    const businessTypeOptions = this.querySelectorAll('.business-type-item');
    businessTypeOptions.forEach((option) => {
      option.classList.remove('selected');
      if (option.dataset.value === this.defaultBusinessType || option.dataset.value === 'all_facilities') {
        option.classList.add('selected');
      }
    });

    // Reset Near Me button
    this.toggleNearMeButton(false);

    // Reset search input
    const searchInput = this.querySelector('.search-input');
    if (searchInput) {
      searchInput.value = '';
    }
    this.hideAutocompleteSuggestions();
  }

  /**
   * Handles the click event for a map marker.
   * Highlights the corresponding sidebar item and the marker.
   **/
  handleMarkerClick(marker, location, isSidebarClicked) {
    const showInfoWindow = () => {
      this.deselectLocation();

      if (!this.infoWindow) {
        this.infoWindow = new google.maps.InfoWindow();
        this.infoWindow.addListener('closeclick', () => {
          this.deselectLocation();
        });
      }

      // Open the infowinw
      this.openInfoWindow(marker, location);

      const sidebarItem = this.findSidebarItem(location);
      const sidebarListContainer = sidebarItem?.closest('.sidebar-list-container');

      if (sidebarItem) {
        this.updateSidebarSelection(location, sidebarItem);

        this.filteredLocations = [
          ...this.filteredLocations.filter((loc) => loc.userId === location.userId),
          ...this.filteredLocations.filter((loc) => loc.userId !== location.userId),
        ];

        if (sidebarListContainer) {
          sidebarListContainer.scrollTo({
            top: sidebarItem.offsetTop - sidebarListContainer.offsetTop,
            behavior: isSidebarClicked ? 'smooth' : 'auto',
          });
        }
      }
    };

    const currentZoom = this.map.getZoom();
    const needsZoom = isSidebarClicked && currentZoom < 13;

    if (needsZoom) {
      google.maps.event.addListenerOnce(this.map, 'idle', () => {
        showInfoWindow();
      });

      this.centerMapUpdateZoomLevel({ lat: location.latitude, lng: location.longitude }, 14);
    } else {
      showInfoWindow();
    }
  }

  openInfoWindow(marker, location, options = {}) {
    // Create and set InfoWindow content
    const infoContent = `<div class="info-window-wrapepr"><h2 class="text-sm font-semibold text-secondary">${location.companyName}</h2></div>`;
    this.infoWindow.setContent(infoContent);

    // Default options, with support for override
    const infoWindowOptions = {
      pixelOffset: new google.maps.Size(0, -12),
      disableAutoPan: options.disableAutoPan ?? false,
    };

    this.infoWindow.setOptions(infoWindowOptions);

    // Open InfoWindow on the specified marker
    this.infoWindow.open({
      map: this.map,
      position: marker.position,
      anchor: marker,
    });

    marker.isSelected = true; // Mark this marker as selected
    marker.zIndex = 2; // Show the selected marekr at top
    marker.content?.classList.add('selected-marker'); // Add custom styling or state class
  }

  /**
   * Finds the corresponding sidebar item for a location
   */
  findSidebarItem(location) {
    return [...document.querySelectorAll('.sidebar-list')].find(
      (item) => item.dataset.locationId === location.userId.toString()
    );
  }

  /**
   * Sorts locations based on their distance from the selected coordinates.
   * Health Pass Partner locations are prioritized and sorted separately from non-partner locations.
   *
   * @param {Array} locations - The list of locations to be sorted.
   * @returns {Array} - A sorted array with Health Pass Partner locations first, followed by non-partner locations.
   */
  sortLocations(locations) {
    // Separate locations into Health Pass Partners and non-partners
    const healthPassLocations = locations.filter((location) => location.isHealthPassPartner);
    const nonHealthPassLocations = locations.filter((location) => !location.isHealthPassPartner);

    // Sorting function to arrange locations by distance from the selected coordinates
    const sortByDistance = (locations) =>
      locations.sort((a, b) => {
        const distanceToA = this.calculateDistance(
          this.selectedLocationCoordinates.lat,
          this.selectedLocationCoordinates.lng,
          parseFloat(a.latitude),
          parseFloat(a.longitude)
        );

        const distanceToB = this.calculateDistance(
          this.selectedLocationCoordinates.lat,
          this.selectedLocationCoordinates.lng,
          parseFloat(b.latitude),
          parseFloat(b.longitude)
        );

        return distanceToA - distanceToB; // Sort in ascending order (nearest first)
      });

    // Return sorted locations with Health Pass Partners first
    return [...sortByDistance(healthPassLocations), ...sortByDistance(nonHealthPassLocations)];
  }

  /**
   * Filters a list of locations based on their distance from a given point and optional business type.
   *
   * @param {Array} locations - An array of location objects. Each object should have `latitude` and `longitude` properties.
   * @param {number} lat - The latitude of the reference point.
   * @param {number} lng - The longitude of the reference point.
   * @param {number} radius - The search radius in miles.
   */
  filterLocations(locations, lat, lng, radius, businessType) {
    radius = radius ? radius * 1609.34 : Infinity;

    let filteredLocations = locations.filter((location) => {
      try {
        const distance = this.calculateDistance(
          lat,
          lng,
          parseFloat(location.latitude),
          parseFloat(location.longitude)
        );

        return distance <= radius;
      } catch (error) {
        return false;
      }
    });

    if (businessType) {
      filteredLocations = filteredLocations.filter(
        (location) => location.businessTypeName?.toLowerCase() === businessType.toLowerCase()
      );
    }

    return filteredLocations;
  }

  /**
   * Calculates the distance between two geographical points using the Google Maps API.
   *
   * @param {number} latitude1 - The latitude of the first point.
   * @param {number} longitude1 - The longitude of the first point.
   * @param {number} latitude2 - The latitude of the second point.
   * @param {number} longitude2 - The longitude of the second point.
   * @returns {number} The distance between the two points in meters.
   */
  calculateDistance(latitude1, longitude1, latitude2, longitude2) {
    return google.maps.geometry.spherical.computeDistanceBetween(
      new google.maps.LatLng(latitude1, longitude1),
      new google.maps.LatLng(latitude2, longitude2)
    );
  }

  /**
   * Centers the map on a given position and updates the zoom level.
   * @param {Object} position - The latitude and longitude coordinates to center the map on.
   * @param {number} zoomLevel - The zoom level to set for the map.
   */
  centerMapUpdateZoomLevel(position, zoomLevel) {
    if (!this.map) return;

    this.map.setCenter(position);
    this.map.setZoom(zoomLevel);
  }

  /**
   * Handles errors that occur during the geolocation retrieval process.
   * Displays an appropriate toast message based on the error type.
   * @param {Object} error - The error object returned by the geolocation API.
   */
  displayGeolocationError(error) {
    switch (error.code) {
      case error.PERMISSION_DENIED:
        showToastMessage(this.geolocationErrorMessage.permissionDenied);
        break;
      case error.POSITION_UNAVAILABLE:
        showToastMessage(this.geolocationErrorMessage.positionUnavailable);
        break;
      case error.TIMEOUT:
        showToastMessage(this.geolocationErrorMessage.timeoutError);
        break;
      case error.UNKNOWN_ERROR:
        showToastMessage(this.geolocationErrorMessage.unknownError);
        break;
    }
  }

  /**
   * Handles the "Near Me" button click event to retrieve the user's current geolocation.
   * If geolocation is supported, it fetches the user's position and processes it.
   */
  handleNearMeClick() {
    if (!navigator.geolocation) {
      showToastMessage('Geolocation is not supported by your browser.');
      return;
    }

    if (!this.isMobile) this.mapLoader.showLoader();

    // Deselect the 'Near Me' button
    if (this.selectedNearByMe) {
      this.selectedNearByMe = null;
      this.isCenterChangeRestricted = false;
      this.selectedLocationCoordinates = this.defaultLocationCoordinates;
      this.locakedCenterPoint = null;
      this.selectedRadius = this.defaultRadius;
      this.isCenterChangeRestricted = false;

      if (this.isEnabledQueryParams) {
        this.updateURLQueryParams(
          this.selectedNearByMe,
          this.selectedInputSearch,
          this.selectedLocationCoordinates.lat,
          this.selectedLocationCoordinates.lng,
          this.selectedRadius,
          this.selectedBusinessType
        );
      }

      // Reset the zoom level and center the map to the default coordinates (USA)
      this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.zoomLevel);
      this.toggleNearMeButton(false); // Toggle the 'Near Me' button to select or deselect it by updating its class

      this.querySelectorAll('.select-radius').forEach((element) => {
        element.classList.remove('selected-option');
      });

      if (!this.isMobile) setTimeout(() => this.mapLoader.hideLoader(), 500);

      return;
    }

    // Get the user's current geolocation and process it.
    navigator.geolocation.getCurrentPosition(
      (position) => {
        this.processGeolocation(position); // Process the position
        this.toggleNearMeButton(true); // Add the selected class

        if (!this.isMobile) setTimeout(() => this.mapLoader.hideLoader(), 500);
      },
      (error) => {
        this.displayGeolocationError(error); // Handle the error
        if (!this.isMobile) setTimeout(() => this.mapLoader.hideLoader(), 500);
      }
    );
  }

  /**
   * Processes the user's current geolocation position and filters locations based on proximity.
   * On mobile, stores the location for later application via "Apply Now" button.
   * On desktop, immediately updates the map.
   * @param {Object} position - The geolocation position object returned by the browser.
   */
  processGeolocation(position) {
    const { latitude, longitude } = position.coords;

    this.selectedNearByMe = true;
    this.isCenterChangeRestricted = true;
    this.selectedInputSearch = null;

    this.selectedLocationCoordinates = {
      lat: latitude,
      lng: longitude,
    };

    this.locakedCenterPoint = {
      lat: latitude,
      lng: longitude,
    };

    // Update URL params and map immediately on desktop or home page. On mobile location page, wait for "Apply Now"
    if (!this.shouldUseDelayedMobileFiltering) {
      if (this.isEnabledQueryParams) {
        this.updateURLQueryParams(
          this.selectedNearByMe,
          this.selectedInputSearch,
          this.selectedLocationCoordinates.lat,
          this.selectedLocationCoordinates.lng,
          this.selectedRadius,
          this.selectedBusinessType
        );
      }

      this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.markerCenterMapZoomLevel);
    }
  }

  /**
   * Searches internal locations by company name or address
   * @param {string} query - The search query
   * @returns {Array} - Array of matching locations
   */
  searchInternalLocations(query) {
    if (!query || !this.allLocations.length) return [];

    const searchTerm = query.toLowerCase().trim();

    return this.allLocations.filter((location) => {
      const companyName = (location.companyName || '').toLowerCase();
      const address = (location.address || '').toLowerCase();

      return companyName.includes(searchTerm) || address.includes(searchTerm);
    });
  }

  /**
   * Handles the search input for location queries and updates the map with filtered locations.
   * @param {string} query - The search query entered by the user.
   */
  async handleInputSearch(query) {
    // If the query is empty, reset to default location and reload locations.
    if (query == '') {
      if (!this.isMobile) this.mapLoader.showLoader();

      this.toggleNearMeButton(false);
      this.hideAutocompleteSuggestions();

      this.selectedLocationCoordinates = this.defaultLocationCoordinates;
      this.locakedCenterPoint = null;
      this.isCenterChangeRestricted = false;
      this.selectedNearByMe = null;
      this.selectedInputSearch = null;
      this.selectedRadius = this.defaultRadius;

      // Update URL params and map immediately on desktop or home page. On mobile location page, wait for "Apply Now"
      if (!this.shouldUseDelayedMobileFiltering) {
        if (this.isEnabledQueryParams) {
          this.updateURLQueryParams(
            this.selectedNearByMe,
            this.selectedInputSearch,
            this.selectedLocationCoordinates.lat,
            this.selectedLocationCoordinates.lng,
            this.selectedRadius,
            this.selectedBusinessType
          );
        }

        // Reset the zoom level and center the map to the default coordinates (USA)
        this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.zoomLevel);

        setTimeout(() => this.mapLoader.hideLoader(), 500);
      }

      return;
    }

    // Search internal locations
    const internalMatches = this.searchInternalLocations(query);

    // Create internal locations suggestions
    const internalSuggestions = internalMatches.map((location) => ({
      text: `${location.companyName} - ${location.address}`,
      isInternal: true,
      location: location,
      magicKey: null,
    }));

    let suggestionList;
    if (internalSuggestions.length > 0) {
      suggestionList = internalSuggestions;
    } else {
      // Fetch Google Places results
      const googleResponse = await LocationApiService.geocodeSearch(query);

      // Create Google suggestions
      suggestionList = googleResponse.suggestions.map((suggestion) => ({
        ...suggestion,
        isInternal: false,
      }));
    }

    // Update the autocomplete suggestions with combined results
    this.updateAutocompleteSuggestions(suggestionList);
  }

  /**
   * Updates the autocomplete suggestions displayed in the UI based on the provided suggestions.
   * Clears existing suggestions and generates a new list based on the search results.
   * @param {Array} suggestions - The list of suggested locations or search results to display.
   */
  updateAutocompleteSuggestions(suggestions) {
    const autocompleteSuggestions = this.querySelector('#autocompleteSuggestions');
    autocompleteSuggestions.innerHTML = '';

    // If there are suggestions, create and display the suggestion list.
    if (suggestions.length > 0) {
      const suggestionList = document.createElement('ul');
      suggestionList.classList.add(
        'max-h-[224px]',
        'overflow-y-auto',
        'custom-scrollbar',
        'flex',
        'flex-col',
        'gap-1.5'
      );

      suggestions.forEach((data) => {
        const suggestionItem = this.createSuggestionItem(data);
        suggestionList.appendChild(suggestionItem);
      });

      autocompleteSuggestions.appendChild(suggestionList);
      autocompleteSuggestions.style.display = 'block';
    } else {
      this.defaultMessage(autocompleteSuggestions);
    }
  }

  /**
   * Displays a default message indicating that no suggestions are available.
   * @param {HTMLElement} autocompleteSuggestions - The container element where suggestions are displayed.
   */
  defaultMessage(autocompleteSuggestions) {
    const message = document.createElement('p');
    message.classList.add('text-gray-8', 'text-sm');
    message.textContent = 'No suggestions available.';

    autocompleteSuggestions.appendChild(message);
    autocompleteSuggestions.style.display = 'block';
  }

  /**
   * Creates a suggestion item for the autocomplete list based on the provided data.
   * This item is clickable and, when clicked, updates the location on the map based on the selected suggestion.
   * @param {Object} data - The suggestion data, containing text and either a magic key for geolocation or internal location data.
   * @returns {HTMLElement} suggestionItem - The created list item element representing the suggestion.
   */
  createSuggestionItem(data) {
    const suggestionItem = document.createElement('li');
    suggestionItem.classList.add('text-gray-8', 'link-item', 'text-base', 'cursor-pointer', 'select-none', 'py-1');

    suggestionItem.textContent = data.text;

    suggestionItem.addEventListener('click', async () => {
      const inputFieldElem = this.querySelector('.search-input');

      // Show loader and update UI immediately
      if (!this.isMobile) this.mapLoader.showLoader();

      inputFieldElem.value = data.text;
      this.hideAutocompleteSuggestions();
      this.toggleNearMeButton(false);

      try {
        if (data.isInternal) {
          // Handle internal location selection
          this.selectedNearByMe = null;
          this.selectedInputSearch = data.text;
          this.isCenterChangeRestricted = true;

          if (this.selectedBusinessType !== data.location.businessTypeName) {
            this.selectedBusinessType = this.defaultBusinessType;
          }

          this.selectedLocationCoordinates = {
            lat: parseFloat(data.location.latitude),
            lng: parseFloat(data.location.longitude),
          };

          // Update map immediately on desktop or home page. On mobile location page, wait for "Apply Now"
          if (!this.shouldUseDelayedMobileFiltering) {
            this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, 12);
          }
        } else {
          const latLong = await LocationApiService.geocodeSearchForLatLong(data.magicKey);

          this.selectedNearByMe = null;
          this.selectedInputSearch = data.text;
          this.isCenterChangeRestricted = true;

          this.selectedLocationCoordinates = {
            lat: latLong.y,
            lng: latLong.x,
          };

          this.locakedCenterPoint = {
            lat: latLong.y,
            lng: latLong.x,
          };

          // Update map immediately on desktop or home page. On mobile location page, wait for "Apply Now"
          if (!this.shouldUseDelayedMobileFiltering) {
            this.centerMapUpdateZoomLevel(this.selectedLocationCoordinates, this.zoomLevel);
          }
        }

        if (!this.isMobile) setTimeout(() => this.mapLoader.hideLoader(), 300);
      } catch (error) {
        console.error('Error processing suggestion:', error);
        if (!this.isMobile) this.mapLoader.hideLoader();
      }
    });

    return suggestionItem;
  }

  /**
   * Hides the autocomplete suggestions dropdown by setting its display to 'none'.
   * This method is typically called when the user clicks outside the suggestions or selects an item.
   */
  hideAutocompleteSuggestions() {
    this.querySelector('#autocompleteSuggestions').style.display = 'none';
  }

  /**
   * Toggles the "Near Me" button's selected state based on the provided boolean value.
   * If the button is enabled, it adds or removes the 'selected' class accordingly.
   * @param {boolean} isSelected - Indicates whether the button should be in the selected state.
   */
  toggleNearMeButton(isSelected) {
    if (this.dataset.enableNearMe == 'true') {
      this.nearMeButtonElem.classList.toggle('selected', isSelected);
    }
  }

  /**
   * Resets all applied filters to their default values.
   *
   * This method clears all selected filters, updates the UI, and resets the URL state.
   * It also ensures that the filter count and visual elements reflect the reset state.
   */
  resetAllFilters() {
    const resetButton = document.getElementById('resetAllFilters');
    if (!resetButton) return; // Skip if button doesn't exist (e.g., on home page)

    resetButton.addEventListener('click', () => {
      const filterIconButton = document.getElementById('filterIcon');
      const dismissModal = document.querySelector('[data-dismiss="modal-selector"]');

      // Show loader while resetting
      this.mapLoader.showLoader();

      // Reset all filter values to default
      this.selectedNearByMe = null;
      this.selectedBusinessType = null;
      this.selectedRadius = this.defaultRadius;
      this.selectedInputSearch = null;
      this.selectedFilterCount = 0;
      this.selectedLocationCoordinates = this.defaultLocationCoordinates;
      this.locakedCenterPoint = null;
      this.isCenterChangeRestricted = false;

      // Reset all mobile filter UI elements
      this.resetMobileFilterUI();

      // Reset UI elements
      this.updateUIElement();

      window.history.replaceState({}, '', window.location.pathname);

      this.updateFilterCount(this.selectedFilterCount);

      filterIconButton.classList.remove('selected');
      filterIconButton.classList.replace('justify-between', 'justify-center');

      // Force clear all markers for a clean reset
      this.clearMarkers();

      // Clear the previous filtered locations cache to force a refresh
      this.previousFilteredLocations = null;

      // Reset map to default location
      this.centerMapUpdateZoomLevel(this.defaultLocationCoordinates, this.zoomLevel);

      // Reload all locations after reset (force update)
      setTimeout(() => {
        if (this.mapBound) {
          this.handleMapChangeEvent(this.mapBound, true);
        }
        this.mapLoader.hideLoader();
      }, 500);

      // Close the filter dialog
      if (dismissModal) {
        dismissModal.click();
      }
    });
  }

  /**
   * Handles the closing of the modal and updates search parameters.
   *
   * This method listens for a click event on an element with the `data-dismiss="modal-selector"`
   * attribute. When clicked, it calls `parseSearchParams()` to process and update search parameters.
   */
  handleClose() {
    const dismissButton = document.querySelector('[data-dismiss="modal-selector"]');

    if (dismissButton) {
      dismissButton.addEventListener('click', () => {
        this.parseSearchParams();
      });
    }
  }

  /**
   * Deselects the currently selected location by:
   * - Closing the info window
   * - Removing selection styles from the sidebar item
   * - Hiding additional info sections
   */
  deselectLocation() {
    // Close info window if it exists
    if (this.infoWindow) {
      this.infoWindow.close();
      this.infoWindow = null;
    }

    // Reset all sidebar items
    this.querySelectorAll('.sidebar-list').forEach((item) => {
      item.classList.remove('selected', 'selected-secondary');
      item.querySelector('.others-info-wrapper')?.classList.add('hidden');
    });

    // Reset marker selection state
    this.mapMarkers.forEach((marker) => {
      marker.isSelected = false;
      marker.zIndex = 0;
      marker.content.classList.remove('selected-marker');
    });
  }

  async checkGeolocationPermission() {
    if (!navigator.geolocation) {
      return;
    }

    try {
      const permissionStatus = await navigator.permissions.query({ name: 'geolocation' });

      // Store the permission result to handle after map initialization
      this.initialGeolocationPermission = {
        state: permissionStatus.state,
        enableNearMe: this.dataset.enableNearMe === 'true',
      };

      // Listen for permission changes
      permissionStatus.addEventListener('change', () => {
        if (permissionStatus.state === 'granted') {
          this.handleNearMeClick();
        } else {
          this.loadDefaultUSALocations();
        }
      });
    } catch (error) {
      this.loadDefaultUSALocations();
    }
  }

  loadDefaultUSALocations() {
    this.selectedLocationCoordinates = this.defaultLocationCoordinates;
    this.isCenterChangeRestricted = false;
    this.selectedNearByMe = null;
    this.selectedRadius = this.defaultRadius;

    // Center map on default USA coordinates only if map is initialized
    if (this.map) {
      this.centerMapUpdateZoomLevel(this.defaultLocationCoordinates, this.zoomLevel);
    }
  }
}

customElements.define('map-location', MapLocation);
