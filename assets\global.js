const EMAIL_VALIDATION_REGEX =
  /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

// SVG icon string representing a default checkmark icon for empty input
const defaultCheckMarkIcon = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="Cross"><path id="Icon" fill-rule="evenodd" clip-rule="evenodd" d="M5.46967 5.46967C5.76256 5.17678 6.23744 5.17678 6.53033 5.46967L12 10.9393L17.4697 5.46967C17.7626 5.17678 18.2374 5.17678 18.5303 5.46967C18.8232 5.76256 18.8232 6.23744 18.5303 6.53033L13.0607 12L18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2374 18.8232 17.7626 18.8232 17.4697 18.5303L12 13.0607L6.53033 18.5303C6.23744 18.8232 5.76256 18.8232 5.46967 18.5303C5.17678 18.2374 5.17678 17.7626 5.46967 17.4697L10.9393 12L5.46967 6.53033C5.17678 6.23744 5.17678 5.76256 5.46967 5.46967Z" fill="#22282F" /></g></svg>`;

// SVG icon string representing a green checkmark icon for valid input
const validCheckMarkIcon = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 21C16.9706 21 21 16.9706 21 12C21 10.2993 20.5283 8.70877 19.7085 7.35213L21.5303 5.53033C21.8232 5.23744 21.8232 4.76256 21.5303 4.46967C21.2374 4.17678 20.7626 4.17678 20.4697 4.46967L18.8164 6.12296C17.166 4.21049 14.7244 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21ZM18.8164 6.12296L12 12.9393L9.53033 10.4697C9.23744 10.1768 8.76256 10.1768 8.46967 10.4697C8.17678 10.7626 8.17678 11.2374 8.46967 11.5303L11.4697 14.5303C11.6103 14.671 11.8011 14.75 12 14.75C12.1989 14.75 12.3897 14.671 12.5303 14.5303L19.7085 7.35213C19.4456 6.91698 19.1468 6.5059 18.8164 6.12296Z" fill="#398D1C"/>
</svg>`;

// SVG icon string representing a red cross icon for invalid input
const invalidCheckMarkIcon = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22ZM9.53033 8.46967C9.23744 8.17678 8.76256 8.17678 8.46967 8.46967C8.17678 8.76256 8.17678 9.23744 8.46967 9.53033L10.9393 12L8.46967 14.4697C8.17678 14.7626 8.17678 15.2374 8.46967 15.5303C8.76256 15.8232 9.23744 15.8232 9.53033 15.5303L12 13.0607L14.4697 15.5303C14.7626 15.8232 15.2374 15.8232 15.5303 15.5303C15.8232 15.2374 15.8232 14.7626 15.5303 14.4697L13.0607 12L15.5303 9.53033C15.8232 9.23744 15.8232 8.76256 15.5303 8.46967C15.2374 8.17678 14.7626 8.17678 14.4697 8.46967L12 10.9393L9.53033 8.46967Z" fill="#D22721"/>
</svg>`;

// Common function to perform fetch request
async function fetchData({ endPoint, method, headers, body, ...otherOptions }) {
  try {
    const response = await fetch(endPoint, {
      method,
      headers,
      body,
      ...otherOptions,
    });
    return response;
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
}

// Function to validate an email address using a regular expression
function isEmailValid(email) {
  return EMAIL_VALIDATION_REGEX.test(email);
}

// Function to check if string length is at least the specified length
function isValidLength(input, minLength) {
  return input.length >= minLength;
}

// Checks if the password contains at least one uppercase and one lowercase letter.
function checkUpperAndLowerCase(input) {
  return /(?=.*[a-z])(?=.*[A-Z])/.test(input);
}

// Function to check if string contains at least one digit
function containsDigit(input) {
  return /\d/.test(input);
}

// Function to check if string contains at least one special character [` ~ ! @ # $ % ^ & * ( ) _ + - = [ ] { } \ | ; : " ' , . < > / ?]
function containsSpecialChar(input) {
  return /[`~!@#$%^&*()_+\-=\[\]{}\\|;:'",.<>\/?]/.test(input);
}

// Function to display a toast message
function showToastMessage(message) {
  const toast = document.getElementById('errorMessage');
  toast.querySelector('.error-message').textContent = message;
  toast.classList.remove('hidden');
  toast.classList.remove('opacity-0');
  toast.style.cssText = 'position: fixed; z-index: 9999';

  // Hide toaster notification after 5 seconds
  setTimeout(() => {
    toast.classList.add('hidden');
    toast.style.cssText = '';
  }, 5000);

  // Event listener to close the toaster notification
  toast.querySelector('button[data-dismiss-target]').addEventListener('click', function () {
    const targetElement = document.getElementById(this.getAttribute('data-dismiss-target'));
    if (targetElement) {
      targetElement.classList.add('hidden');
      toast.style.position = '';
    }
  });
}

// Function to set data in session storage
function setSessionStorageItem(key, value) {
  sessionStorage.setItem(key, JSON.stringify(value));
}

// Function to get data from session storage
function getSessionStorageItem(key) {
  return JSON.parse(sessionStorage.getItem(key));
}

// Function to delete an item from session storage
function deleteSessionStorageItem(key) {
  sessionStorage.removeItem(key);
}

// Function to set data into localStorage
function setLocalStorageItem(key, value) {
  localStorage.setItem(key, JSON.stringify(value));
}

// Function to get data from localStorage
function getLocalStorageItem(key) {
  return JSON.parse(localStorage.getItem(key));
}

// Function to delete an item from local storage
function deleteLocalStorageItem(key) {
  localStorage.removeItem(key);
}

// Function to update password validation icon
function updatePasswordValidationIcon(messageElement, isValid) {
  const iconElement = messageElement.querySelector('.cross-icon');
  iconElement.innerHTML = isValid ? validCheckMarkIcon : invalidCheckMarkIcon;
}

// Function to update password validation message and styling
function updatePasswordValidationMessage(messageElement, isValid) {
  const iconElement = messageElement.querySelector('.cross-icon');
  iconElement.classList.remove('opacity-55');
  messageElement.classList.toggle('valid', isValid);
  messageElement.classList.toggle('invalid', !isValid);
}

// Function to reload the page when the browser's history state changes (e.g., navigating back or forward)
function reloadOnPopState() {
  window.addEventListener('popstate', function () {
    window.location.reload();
  });
}

// Function to formats a date string into a specific format
function formatDate(dateString) {
  if (!dateString) {
    return '-';
  }

  const date = new Date(dateString);

  const formattedDate = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  return formattedDate.replace(',', ' ');
}

/* Function to toggle preloader visibility
Call toggleFullScreenPreloader(true) to display the full-screen preloader,
and call toggleFullScreenPreloader(false) to hide the full-screen preloader.*/
function toggleFullScreenPreloader(visible) {
  const preloader = document.getElementById('loader');
  if (visible) {
    if (!preloader) {
      const preloader = document.createElement('div');
      preloader.classList.add('preloader');
      preloader.id = 'loader';
      preloader.innerHTML = '<div class="preloader-circle"></div>';
      document.body.appendChild(preloader);
    } else {
      preloader.classList.remove('preloader-invisible');
    }
    document.querySelector('html').style.overflowY = 'hidden';
  } else {
    if (preloader) {
      preloader.classList.add('preloader-invisible');
      setTimeout(() => preloader.remove(), 500);
    }
    document.querySelector('html').style.overflowY = '';
  }
}

/**
 * Adds data to the browser's history state and optionally reloads the page
 * @param {Object} dataObj - The data object to add to the history state
 * @param {string} fallbackUrl - The URL to push to the history state if paramName is not found
 * @param {string} paramName - The URL parameter name to check for in the current URL
 */
function updateHistoryState(dataObj, fallbackUrl) {
  var currentState = window.history.state || {};
  const url = new URL(window.location.href);
  const paramValue = url.searchParams.get(QUERY_PARAMS_KEY.CHECKOUT_URL);

  // Create a new URL object with the fallbackUrl
  const newUrlWithParams = new URL(fallbackUrl, url);

  if (paramValue) {
    // Modify the searchParams of the newUrlWithParams
    newUrlWithParams.searchParams.set(QUERY_PARAMS_KEY.CHECKOUT_URL, paramValue);

    // Create a new state with updated data
    var newState = Object.assign({}, currentState, dataObj);

    window.history.pushState(newState, '', newUrlWithParams);
  } else {
    var newState = Object.assign({}, currentState, dataObj);
    window.history.pushState(newState, '', fallbackUrl);
  }

  location.reload();
}

/**
 * Reads data from the browser's history state based on the specified key
 * @param {string} key - The key to retrieve data from the history state
 * @returns {any} The value associated with the specified key in the history state, or null if not found
 */
function readFromHistoryState(key) {
  var currentState = window.history.state;

  // Check if state exists and contains the specified key
  if (currentState && currentState.hasOwnProperty(key)) {
    return currentState[key];
  } else {
    // Key not found in state or state is null
    return null;
  }
}

/**
 * Deletes a specific item from the browser's history state
 * @param {string} key - The key of the item to set to null
 */
function deleteHistoryStateItem(key) {
  var currentState = window.history.state || {};
  // Delete the specified key from the current state object
  delete currentState[key];
}

/**
 * Handles redirection based on the presence of a specific query parameter.
 *
 * @param {string} paramName - The name of the query parameter to check for.
 * @param {string} fallbackUrl - The URL to redirect to if the query parameter is not found.
 */
function handleQueryParamRedirect(paramName, fallbackUrl) {
  const url = new URL(window.location.href);
  const paramValue = url.searchParams.get(paramName);

  if (paramValue) {
    window.location.href = PAGE_URLS.CART;
  } else {
    window.location.replace(fallbackUrl);
  }
}

function handleCheckout() {
  const checkoutElements = document.querySelectorAll('button[data-checkout]');

  checkoutElements.forEach(function (checkoutElement) {
    checkoutElement.addEventListener('click', function (event) {
      event.preventDefault();

      const checkoutObj = { checkout: 'checkout' };
      updateHistoryState(checkoutObj, PAGE_URLS.SHOPIFY_LOGIN);
    });
  });
}

/**
 * Initializes the accordion functionality.
 * Toggles the `aria-expanded` attribute of buttons to expand/collapse items.
 * @param {string} selector - The CSS selector for the accordion buttons.
 */
function initializeAccordion(selector) {
  const accordionItems = document.querySelectorAll(selector);

  accordionItems.forEach((item) => {
    item.addEventListener('click', function () {
      const isExpanded = this.getAttribute('aria-expanded') === 'true';
      this.setAttribute('aria-expanded', isExpanded ? 'false' : 'true');
    });
  });
}

// Wait until the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function () {
  // Handles the checkout functionality
  handleCheckout();

  if (window.location.pathname === PAGE_URLS.SUBSCRIPTIONS) {
    // Reload the page when navigating using browser back/forward buttons
    reloadOnPopState();
  }

  // Initialize accordion functionality for buttons within `.accordion`
  initializeAccordion('.accordion button');
});
