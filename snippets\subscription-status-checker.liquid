{% comment %}
  Reusable Subscription Status Checker

  This snippet checks the customer's subscription status and makes variables
  available in the parent scope using liquid tags.

  Variables made available:
  - is_subscription_active: true if customer has an active subscription
  - is_subscription_cancelled: true if customer's subscription is cancelled
  - is_subscription_paused: true if customer's subscription is paused
  - is_subscription_expired: true if customer's subscription is expired
  - is_subscription_failed: true if customer's subscription failed
  - latest_subscription: the most recent subscription object
  - scans_count: available scan bundles from customer metafields
  - has_health_pass_in_cart: true if cart contains a health-pass product

  Usage:
  {% liquid render 'subscription-status-checker' %}

  Optional parameters:
  - check_cart: set to true to also check for health-pass items in cart (default: false)

  Example with cart checking:
  {% liquid render 'subscription-status-checker', check_cart: true %}
{% endcomment %}

{% comment %} Reset all subscription status variables {% endcomment %}
{% assign is_subscription_active = false %}
{% assign is_subscription_cancelled = false %}
{% assign is_subscription_paused = false %}
{% assign is_subscription_expired = false %}
{% assign is_subscription_failed = false %}
{% assign latest_subscription = null %}

{% comment %} Get scan counts from customer metafields {% endcomment %}
{% assign scans_count = customer.metafields.styku.available_scan_bundles.value %}

{% comment %} Get and sort subscriptions by created_at (newest first) {% endcomment %}
{% assign all_subscriptions = customer.metafields.appstle_subscription.subscriptions.value %}

{% if all_subscriptions and all_subscriptions.size > 0 %}
  {% assign sorted_subscriptions = all_subscriptions | sort: 'created_at' | reverse %}
  {% assign latest_subscription = sorted_subscriptions[0] %}

  {% comment %} Set status flags based on the latest subscription {% endcomment %}
  {% case latest_subscription.status %}
    {% when 'active' %}
      {% assign is_subscription_active = true %}
    {% when 'cancelled' %}
      {% assign is_subscription_cancelled = true %}
    {% when 'paused' %}
      {% assign is_subscription_paused = true %}
    {% when 'expired' %}
      {% assign is_subscription_expired = true %}
    {% when 'failed' %}
      {% assign is_subscription_failed = true %}
  {% endcase %}
{% endif %}

{% comment %} Check for health-pass items in cart if requested {% endcomment %}
{% assign has_health_pass_in_cart = false %}

{% if check_cart == true and cart.items.size > 0 %}
  {% for cart_item in cart.items %}
    {% if cart_item.product.type == 'health-pass' %}
      {% assign has_health_pass_in_cart = true %}
      {% break %}
    {% endif %}
  {% endfor %}
{% endif %}

{% comment %}
  Debug output (remove in production)
  Uncomment the lines below for debugging purposes
{% endcomment %}

{% comment %}
  <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px;">
    <strong>Subscription Status Debug:</strong><br>
    Active: {{ is_subscription_active }}<br>
    Cancelled: {{ is_subscription_cancelled }}<br>
    Paused: {{ is_subscription_paused }}<br>
    Expired: {{ is_subscription_expired }}<br>
    Failed: {{ is_subscription_failed }}<br>
    Scan Counts: {{ scan_counts }}<br>
    {% if check_cart == true %}
      Health Pass in Cart: {{ has_health_pass_in_cart }}<br>
    {% endif %}
    {% if latest_subscription %}
      Latest Subscription ID: {{ latest_subscription.id }}<br>
      Latest Subscription Status: {{ latest_subscription.status }}<br>
    {% endif %}
  </div>
{% endcomment %}
