{% liquid
  assign is_subscription_active = false
  assign is_subscription_cancelled = false
  assign is_subscription_paused = false
  assign is_subscription_expired = false
  assign is_subscription_failed = false
  assign latest_subscription = null

  assign scans_count = customer.metafields.styku.available_scan_bundles.value

  assign all_subscriptions = customer.metafields.appstle_subscription.subscriptions.value

  if all_subscriptions and all_subscriptions.size > 0
    assign sorted_subscriptions = all_subscriptions | sort: 'created_at' | reverse
    assign latest_subscription = sorted_subscriptions[0]

    case latest_subscription.status
      when 'active'
        assign is_subscription_active = true
      when 'cancelled'
        assign is_subscription_cancelled = true
      when 'paused'
        assign is_subscription_paused = true
      when 'expired'
        assign is_subscription_expired = true
      when 'failed'
        assign is_subscription_failed = true
    endcase
  endif

  assign has_health_pass_in_cart = false

  for cart_item in cart.items
    if cart_item.product.type == 'health-pass'
      assign has_health_pass_in_cart = true
      break
    endif
  endfor
%}

<div id="cartWrapper" class="container px-4 lg:px-0 mt-9 mb-8 md:mt-14">
  {% render 'empty-cart',
    title_classes: 'text-3xl md:text-4xl font-bold text-secondary mt-6 mb-3',
    paragraph_width: 'lg:w-[46%]'
  %}
  <form
    action="{{ routes.cart_url }}"
    method="post"
    novalidate
    class="cart-item-form-block grid grid-cols-1 lg:grid-cols-3 gap-6"
  >
    <div class="cart-block bg-white md:col-span-2">
      <div class="cart-item-wrapper flex flex-col gap-4">
        {% if cart.item_count != 0 %}
          {% render 'customer-order-comparison-for-cart-items', cart_items: cart.items %}
        {% endif %}
      </div>
    </div>
    {% if cart.item_count != 0 %}
      <div class="checkout-button-block space-y-4">
        <div class="border border-gray-2 rounded-lg p-4">
          <h3 id="cart-popup-label" class="text-3xl md:text-4xl font-bold text-secondary mb-4">
            {{ 'cart.summary' | t }}
          </h3>
          <div id="cart-popup-subtotal" class="flex justify-between">
            <p class="paragraph-text !font-bold">{{ 'cart.total' | t }}</p>
            <p class="text-base">
              {% if cart.items_subtotal_price > cart.total_price %}
                <s class="text-base font-bold text-gray-8">
                  {{ cart.items_subtotal_price | money }}
                </s>
                <b>
                  {{ cart.total_price | money }}
                </b>
              {% else %}
                <b class="text-base font-bold text-gray-8">
                  {{ cart.total_price | money }}
                </b>
              {% endif %}
            </p>
          </div>

          {% for line_item in cart.items %}
            {% if line_item.selling_plan_allocation.selling_plan %}
              <div class="recurring-subtotal-block flex items-center justify-between gap-2">
                <p class="text-sm text-gray-5 flex gap-2 items-center">
                  {{ 'cart.recurring_subtotal' | t }}
                  <span>{{- 'icon-question-mark-circle.svg' | inline_asset_content -}}</span>
                </p>
                <p class="text-sm text-gray-5">
                  {{ line_item.selling_plan_allocation.per_delivery_price | money }}
                  {% if line_item.selling_plan_allocation.selling_plan.name contains 'Annual' %}
                    {{ 'cart.every_year' | t }}
                  {% else %}
                    {{ 'cart.every_month' | t }}
                  {% endif %}
                </p>
              </div>
            {% endif %}
          {% endfor %}
        </div>
        {% render 'checkout-button',
          classes: 'button-primary-gradient flex justify-center items-center gap-4 text-center !text-base w-full !py-3',
          is_subscription_active: is_subscription_active,
          is_subscription_cancelled: is_subscription_cancelled,
          is_subscription_expired: is_subscription_expired,
          is_subscription_failed: is_subscription_failed,
          has_health_pass_in_cart: has_health_pass_in_cart
        %}
        {% render 'alert-message', cart_items: cart.items %}
      </div>
    {% endif %}
  </form>
</div>
