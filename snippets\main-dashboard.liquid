{% liquid
  assign latest_subscription = latest_subscription
  assign is_subscription_active = is_subscription_active
  assign is_subscription_cancelled = is_subscription_cancelled
  assign is_subscription_expired = is_subscription_expired
  assign is_subscription_paused = is_subscription_paused
  assign is_subscription_failed = is_subscription_failed
  assign scans_count = scans_count
%}

<div class="profile-information space-y-6">
  <div class="see-block-test-container border border-gray-2 relative overflow-hidden rounded-2xl mb-6 md:mb-0">
    <div
      class="absolute left-0 h-full w-full z-[1]"
      style="background: linear-gradient(90deg,#FFF  62%,  rgba(255, 255, 255, 0.00) 100%);"
    ></div>
    {% liquid
      assign width = '-right-48 md:-right-0'
      if scans_count == 0
        assign width = '-right-48 md:-right-40'
      else
        assign width = '-right-48 md:-right-0'
      endif
    %}
    <div class="absolute top-0 h-full z-0 {{ width -}}">
      <img
        src="https://cdn.shopify.com/s/files/1/0575/7678/3970/files/availble-scans-banner.png?v=1749131811"
        loading="lazy"
        class="h-full object-cover"
        width="auto"
        height="auto"
      >
    </div>
    <div class="see-blood-test-block p-6 md:p-9 relative z-[1]">
      {% if is_subscription_active or is_subscription_cancelled %}
        <div class="content-block button-primary-light py-2 !px-3 inline-flex mb-2">
          <span class="text-primary-gradient text-sm">{{- 'general.customer.membership' | t -}}</span>
        </div>
      {% endif %}

      {% if scans_count >= 0 %}
        <h2 class="text-secondary font-bold text-2xl md:text-[28px] md:leading-none mb-4">
          {{ 'general.customer.scans_available' | t }}
        </h2>
        <div class="pay-per-scan-block flex">
          <div class="content-block flex flex-col md:flex-row items-center gap-4 md:gap-32">
            <div class="scans-available w-full {% if scans_count == 0 %}md:w-1/2{% endif %}">
              <p
                id="payPerScanScansCount"
                class="text-base text-secondary font-bold flex items-center gap-2"
              >
                <span class="text-[64px] leading-[68px] font-bold">{{- scans_count -}}</span>
                <span class="text-base font-bold text-secondary max-w-52">
                  {{- 'general.customer.scan_package_bundle' | t -}}
                </span>
              </p>
              {% if scans_count == 0 %}
                <p class="text-xs text-gray-8 mb-3">
                  You’ve completed all 4 Preventative Body Scans in your subscription. You can still purchase a single
                  scan anytime until your plan renews.
                </p>
                <a
                  href="/pages/pricing"
                  class="button-primary-gradient inline-flex justify-center items-center gap-4 !px-16 !text-base !py-3"
                >
                  {{- 'Get a Scan' }}
                </a>
              {% endif %}
            </div>
          </div>
        </div>
      {% else %}
        <h2 class="text-secondary font-bold  text-2xl md:text-[28px] md:leading-none mb-2">
          {{ 'general.customer.no_scans_available' | t }}
        </h2>
        <p class="text-base text-gray-8 mb-3">{{ 'general.customer.no_scans_available_message' | t }}</p>
        <a
          href="/pages/locations"
          class="button-primary-gradient inline-flex justify-center items-center gap-4  !px-8  md:!px-20 !text-base !py-3"
        >
          {{ 'Find location' }}
        </a>
      {% endif %}
    </div>
  </div>

  <div class="dashboard rounded-2xl border border-gray-2 p-4 md:p-6">
    <div class="header-block mb-4">
      <h3 class="heading-level-3">{{ 'general.customer.dashboard' | t }}</h3>
    </div>
    <div class="content-block grid grid-cols-1 md:grid-cols-2 gap-4">
      <div class="number-of-scans bg-gray-1 rounded-2xl p-4">
        <p class="text-base text-secondary font-bold mb-2">{{ 'general.customer.number_of_scans' | t }}</p>
        <p class="number-of-scans-count paragraph-text"></p>
      </div>
      <div class="last-scan bg-gray-1 rounded-2xl p-4">
        <p class="text-base text-secondary font-bold mb-2">{{ 'general.customer.last_scan' | t }}</p>
        <p class="last-scan-date paragraph-text"></p>
      </div>
    </div>
  </div>
  <div class="billing-and-payments rounded-2xl border border-gray-2 p-4 md:p-6">
    <div class="header-block mb-4">
      <h3 class="heading-level-3">{{ 'general.customer.billing_and_payments' | t }}</h3>
    </div>
    <div class="content-block">
      <a href="#billing-payments" class="button-primary-light !w-52 py-2">
        <span class="text-primary-gradient">{{- 'general.customer.manage' | t -}}</span>
      </a>
    </div>
  </div>
  <div class="profile-information rounded-2xl border border-gray-2 p-4 md:p-6">
    <div class="header-block mb-4">
      <h3 class="heading-level-3">{{- 'general.customer.profile_info' | t -}}</h3>
    </div>
    <div class="content-block space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-4">
        <div class="input-block space-y-2">
          <label class="block text-base text-secondary font-bold">{{- 'input.first_name.label' | t -}}</label>
          <input
            type="text"
            id="firstName"
            name="firstName"
            value="{{ customer.first_name |  capitalize }}"
            readonly
            class="bg-white opacity-55 select-none cursor-not-allowed border text-base border-gray-2 text-secondary placeholder-gray-4 rounded-md w-full py-2.5 px-3 focus:border-gray-2 focus:outline-none"
          >
        </div>
        <div class="input-block space-y-2">
          <label class="block text-base text-secondary font-bold">{{ 'input.last_name.label' | t }}</label>
          <input
            type="text"
            id="lastName"
            name="lastName"
            value="{{- customer.last_name  | capitalize -}}"
            readonly
            class="bg-white opacity-55 select-none cursor-not-allowed border text-base border-gray-2 text-secondary placeholder-gray-4 rounded-md w-full py-2.5 px-3 focus:border-gray-2 focus:outline-none"
          >
        </div>
        <div class="input-block space-y-2">
          <label class="block  text-base text-secondary font-bold">{{- 'input.email.label' | t -}}</label>
          <input
            type="email"
            name="email"
            id="email"
            readonly
            class="cursor-not-allowed opacity-70 bg-[#E6E6E6] border text-base border-gray-2 text-secondary placeholder-gray-4 rounded-md w-full py-2.5 px-3 focus:border-gray-2 focus:outline-none"
            value="{{ customer.email }}"
          >
        </div>
        <div class="input-block space-y-2">
          <label class="block text-base text-secondary font-bold">{{- 'input.password.label' | t -}}</label>
          <button
            data-modal-target="changePasswordModal"
            data-modal-toggle="changePasswordModal"
            class="button-secondary !font-bold !mt-2"
          >
            {{- 'general.customer.change_password' | t -}}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
{% render 'change-password' %}
<script src="{{ 'change-password.js' |  asset_url }}" defer></script>
