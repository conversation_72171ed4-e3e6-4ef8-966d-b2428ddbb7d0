{% comment %}
  Reusable Subscription Logic
  
  This snippet contains the subscription status checking logic that can be copied
  and used in multiple files. Since Liquid doesn't allow snippets to set variables
  in parent scope, this serves as a template for the logic.
  
  Copy this liquid block into your file where you need subscription status:
{% endcomment %}

{% liquid
  comment 'Reset all subscription status variables'
  assign is_subscription_active = false
  assign is_subscription_cancelled = false
  assign is_subscription_paused = false
  assign is_subscription_expired = false
  assign is_subscription_failed = false
  assign latest_subscription = null
  
  comment 'Get scan counts from customer metafields'
  assign scans_count = customer.metafields.styku.available_scan_bundles.value
  
  comment 'Get and sort subscriptions by created_at (newest first)'
  assign all_subscriptions = customer.metafields.appstle_subscription.subscriptions.value
  
  if all_subscriptions and all_subscriptions.size > 0
    assign sorted_subscriptions = all_subscriptions | sort: 'created_at' | reverse
    assign latest_subscription = sorted_subscriptions[0]
    
    comment 'Set status flags based on the latest subscription'
    case latest_subscription.status
      when 'active'
        assign is_subscription_active = true
      when 'cancelled'
        assign is_subscription_cancelled = true
      when 'paused'
        assign is_subscription_paused = true
      when 'expired'
        assign is_subscription_expired = true
      when 'failed'
        assign is_subscription_failed = true
    endcase
  endif
  
  comment 'Check for health-pass items in cart (optional - include if needed)'
  assign has_health_pass_in_cart = false
  
  for cart_item in cart.items
    if cart_item.product.type == 'health-pass'
      assign has_health_pass_in_cart = true
      break
    endif
  endfor
%}
