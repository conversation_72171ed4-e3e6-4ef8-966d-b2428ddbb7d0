{% liquid
  assign subscriptions = customer.metafields.appstle_subscription.subscriptions.value
  assign scans_count = customer.metafields.styku.available_scan_bundles.value

  if subscriptions and subscriptions.size > 0
    case subscriptions[0].status
      when 'active'
        assign is_subscription_active = true
      when 'cancelled'
        assign is_subscription_cancelled = true
      when 'paused'
        assign is_subscription_paused = true
      when 'expired'
        assign is_subscription_expired = true
      when 'failed'
        assign is_subscription_failed = true
    endcase
  endif
%}
{{ subscriptions }}

<div class="container px-4 lg:px-0">
  <div class="main-wrapper mx-auto mb-7 mt-9 md:mb-11 md:mt-11">
    <div class="customer-section flex flex-col md:flex-row items-start gap-6">
      <aside class="sidebar-navigation w-full grid grid-cols-1 md:max-w-[345px] top-[137px] md:sticky">
        <div class="navigation-box rounded-2xl border border-gray-2 p-4 md:p-6 ">
          <div class="user-profile block">
            <div class="grid divide-y divide-neutral-200 mx-auto">
              <details class="group" open>
                <summary class="flex gap-2 items-center font-medium cursor-pointer md:hidden">
                  <span class="summery-heading-title block w-full text-base">
                    {{- 'general.customer.profile_info' | t -}}
                  </span>
                  <span class="transition group-open:rotate-180">
                    {% render 'chevron-down-icon' %}
                  </span>
                </summary>
                <ul class="profile-link-block flex flex-col gap-4 mt-4 md:mt-0">
                  {%- for link in linklists['account-menu'].links -%}
                    <li class="block w-full text-base">
                      <a href="{{ link.url }}" data-block-id="{{ link.handle }}" class="sidebar-link-item">
                        {{ link.title }}
                      </a>
                    </li>
                  {% endfor %}
                </ul>
              </details>
            </div>
          </div>
        </div>
      </aside>
      <div class="main-content w-full">
        <div id="profile-information" class="membership-block main-dashboard">
          {% render 'main-dashboard',
            is_subscription_active: is_subscription_active,
            is_subscription_cancelled: is_subscription_cancelled,
            is_subscription_expired: is_subscription_expired,
            is_subscription_failed: is_subscription_failed,
            is_subscription_paused: is_subscription_paused,
            scans_count: scans_count
          %}
        </div>
        <div id="billing-payments" class="membership-block billing-payments hidden">
          {% render 'billing-and-payments' %}
        </div>
        <div id="subscriptions" class="membership-block membership hidden">
          {% render 'membership',
            is_subscription_active: is_subscription_active,
            is_subscription_cancelled: is_subscription_cancelled,
            is_subscription_expired: is_subscription_expired,
            is_subscription_failed: is_subscription_failed,
            is_subscription_paused: is_subscription_paused,
            scans_count: scans_count
          %}
        </div>
        <div id="at-home-diagnostics" class="membership-block at-home-diagnostics-information hidden">
          {% render 'at-home-diagnostics' %}
        </div>
        <div id="faq" class="membership-block faq hidden ">
          {% render 'faq' %}
        </div>
      </div>
    </div>
  </div>
</div>
<script src="{{ 'profile-information.js' | asset_url }}" defer></script>
<script src="{{ 'toggle-password-visibility.js' | asset_url }}" defer></script>
