{% liquid
  assign is_subscription_active = false
  assign is_subscription_cancelled = false
  assign is_subscription_paused = false
  assign is_subscription_expired = false
  assign is_subscription_failed = false
  assign latest_subscription = null

  assign scans_count = customer.metafields.styku.available_scan_bundles.value

  assign all_subscriptions = customer.metafields.appstle_subscription.subscriptions.value

  if all_subscriptions and all_subscriptions.size > 0
    assign sorted_subscriptions = all_subscriptions | sort: 'created_at' | reverse
    assign latest_subscription = sorted_subscriptions[0]

    case latest_subscription.status
      when 'active'
        assign is_subscription_active = true
      when 'cancelled'
        assign is_subscription_cancelled = true
      when 'paused'
        assign is_subscription_paused = true
      when 'expired'
        assign is_subscription_expired = true
      when 'failed'
        assign is_subscription_failed = true
    endcase
  endif

  assign has_health_pass_in_cart = false

  for cart_item in cart.items
    if cart_item.product.type == 'health-pass'
      assign has_health_pass_in_cart = true
      break
    endif
  endfor
%}

{% render 'cart-items',
  cart_popup: cart_popup,
  sidebar_item: sidebar_item,
  list_wrapper_classes: list_wrapper_classes,
  cart_items: cart.items,
  title_classes: title_classes,
  scan_counts: scan_counts,
  is_subscription_active: is_subscription_active,
  is_subscription_cancelled: is_subscription_cancelled,
  is_subscription_expired: is_subscription_expired,
  is_subscription_failed: is_subscription_failed,
  has_health_pass_in_cart: has_health_pass_in_cart
%}
