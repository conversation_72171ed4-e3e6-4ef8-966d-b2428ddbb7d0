/**
 * Referral Tracking Script
 * Stores tracking parameters in localStorage and preserves them in the query string.
 * Parameters: ps_partner_key, ps_xid, gsxid, gspk, location_id
 */
class ReferralTracking {
  constructor() {
    this.trackingParams = ['ps_partner_key', 'ps_xid', 'gsxid', 'gspk', 'location_id'];
    this.init();
  }

  init() {
    this.checkAndStoreReferralData();
  }

  /**
   * Check for referral parameters in the URL and store them in localStorage
   */
  checkAndStoreReferralData() {
    const urlParams = new URLSearchParams(window.location.search);
    const trackingData = {};
    let hasTrackingParams = false;

    this.trackingParams.forEach((param) => {
      const value = urlParams.get(param);
      if (value) {
        trackingData[param] = value;
        hasTrackingParams = true;
      }
    });

    if (hasTrackingParams) {
      this.storeInLocalStorage(trackingData);
    }
  }

  /**
   * Store data in localStorage
   * @param {Object} data
   */
  storeInLocalStorage(data) {
    try {
      setLocalStorageItem(LOCAL_STORAGE_KEY.REFERRAL_DATA, data);
    } catch (e) {
      console.warn('Referral tracking: Failed to store in localStorage', e);
    }
  }

  /**
   * Retrieve referral data from localStorage
   */
  getTrackingDataFromLocal() {
    try {
      const data = getLocalStorageItem(LOCAL_STORAGE_KEY.REFERRAL_DATA);
      return data || {};
    } catch (e) {
      console.warn('Referral tracking: Failed to read from localStorage', e);
      return {};
    }
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.ReferralTracking = new ReferralTracking();
  });
} else {
  window.ReferralTracking = new ReferralTracking();
}
