/*
! tailwindcss v3.4.1 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden] {
  display: none;
}

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}

.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 640px) {
  .container {
    max-width: 100%;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 100%;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1216px;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.inset-0 {
  inset: 0px;
}

.inset-x-0 {
  left: 0px;
  right: 0px;
}

.inset-y-0 {
  top: 0px;
  bottom: 0px;
}

.\!-right-12 {
  right: -3rem !important;
}

.-bottom-4 {
  bottom: -1rem;
}

.-right-48 {
  right: -12rem;
}

.-top-14 {
  top: -3.5rem;
}

.-top-5 {
  top: -1.25rem;
}

.-top-6 {
  top: -1.5rem;
}

.bottom-0 {
  bottom: 0px;
}

.bottom-40 {
  bottom: 10rem;
}

.bottom-6 {
  bottom: 1.5rem;
}

.bottom-\[-12px\] {
  bottom: -12px;
}

.bottom-\[-22px\] {
  bottom: -22px;
}

.bottom-\[60px\] {
  bottom: 60px;
}

.end-0 {
  inset-inline-end: 0px;
}

.end-2 {
  inset-inline-end: 0.5rem;
}

.end-2\.5 {
  inset-inline-end: 0.625rem;
}

.left-0 {
  left: 0px;
}

.left-1\/2 {
  left: 50%;
}

.left-28 {
  left: 7rem;
}

.left-6 {
  left: 1.5rem;
}

.left-7 {
  left: 1.75rem;
}

.left-\[4\.60rem\] {
  left: 4.60rem;
}

.left-\[45\%\] {
  left: 45%;
}

.right-0 {
  right: 0px;
}

.right-2 {
  right: 0.5rem;
}

.right-6 {
  right: 1.5rem;
}

.right-auto {
  right: auto;
}

.start-0 {
  inset-inline-start: 0px;
}

.top-0 {
  top: 0px;
}

.top-16 {
  top: 4rem;
}

.top-2 {
  top: 0.5rem;
}

.top-2\.5 {
  top: 0.625rem;
}

.top-2\/4 {
  top: 50%;
}

.top-3 {
  top: 0.75rem;
}

.top-64 {
  top: 16rem;
}

.top-\[132px\] {
  top: 132px;
}

.top-\[137px\] {
  top: 137px;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[1\] {
  z-index: 1;
}

.z-\[2\] {
  z-index: 2;
}

.z-\[999\] {
  z-index: 999;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-first {
  order: -9999;
}

.order-last {
  order: 9999;
}

.order-none {
  order: 0;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.col-start-1 {
  grid-column-start: 1;
}

.col-start-3 {
  grid-column-start: 3;
}

.col-end-5 {
  grid-column-end: 5;
}

.row-span-2 {
  grid-row: span 2 / span 2;
}

.row-span-3 {
  grid-row: span 3 / span 3;
}

.row-start-2 {
  grid-row-start: 2;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-16 {
  margin-top: 4rem;
  margin-bottom: 4rem;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.\!mt-0 {
  margin-top: 0px !important;
}

.\!mt-2 {
  margin-top: 0.5rem !important;
}

.\!mt-4 {
  margin-top: 1rem !important;
}

.\!mt-8 {
  margin-top: 2rem !important;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-1\.5 {
  margin-bottom: 0.375rem;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.mb-11 {
  margin-bottom: 2.75rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-2\.5 {
  margin-bottom: 0.625rem;
}

.mb-20 {
  margin-bottom: 5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-3\.5 {
  margin-bottom: 0.875rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-7 {
  margin-bottom: 1.75rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-9 {
  margin-bottom: 2.25rem;
}

.me-4 {
  margin-inline-end: 1rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-4 {
  margin-left: 1rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-1\.5 {
  margin-right: 0.375rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.ms-2 {
  margin-inline-start: 0.5rem;
}

.ms-3 {
  margin-inline-start: 0.75rem;
}

.ms-auto {
  margin-inline-start: auto;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-1\.5 {
  margin-top: 0.375rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-16 {
  margin-top: 4rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-32 {
  margin-top: 8rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-7 {
  margin-top: 1.75rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-9 {
  margin-top: 2.25rem;
}

.mt-\[18px\] {
  margin-top: 18px;
}

.mt-auto {
  margin-top: auto;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.inline-grid {
  display: inline-grid;
}

.contents {
  display: contents;
}

.hidden {
  display: none;
}

.size-16 {
  width: 4rem;
  height: 4rem;
}

.size-3 {
  width: 0.75rem;
  height: 0.75rem;
}

.size-3\.5 {
  width: 0.875rem;
  height: 0.875rem;
}

.size-32 {
  width: 8rem;
  height: 8rem;
}

.size-4 {
  width: 1rem;
  height: 1rem;
}

.size-44 {
  width: 11rem;
  height: 11rem;
}

.size-5 {
  width: 1.25rem;
  height: 1.25rem;
}

.size-6 {
  width: 1.5rem;
  height: 1.5rem;
}

.size-7 {
  width: 1.75rem;
  height: 1.75rem;
}

.size-\[77px\] {
  width: 77px;
  height: 77px;
}

.size-full {
  width: 100%;
  height: 100%;
}

.h-10 {
  height: 2.5rem;
}

.h-11 {
  height: 2.75rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-16 {
  height: 4rem;
}

.h-4 {
  height: 1rem;
}

.h-56 {
  height: 14rem;
}

.h-6 {
  height: 1.5rem;
}

.h-7 {
  height: 1.75rem;
}

.h-8 {
  height: 2rem;
}

.h-9 {
  height: 2.25rem;
}

.h-\[20px\] {
  height: 20px;
}

.h-\[22px\] {
  height: 22px;
}

.h-\[270px\] {
  height: 270px;
}

.h-\[365px\] {
  height: 365px;
}

.h-\[392px\] {
  height: 392px;
}

.h-\[60px\] {
  height: 60px;
}

.h-\[calc\(100\%-1rem\)\] {
  height: calc(100% - 1rem);
}

.h-\[calc\(100vh-250px\)\] {
  height: calc(100vh - 250px);
}

.h-auto {
  height: auto;
}

.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}

.h-full {
  height: 100%;
}

.h-screen {
  height: 100vh;
}

.max-h-16 {
  max-height: 4rem;
}

.max-h-24 {
  max-height: 6rem;
}

.max-h-\[224px\] {
  max-height: 224px;
}

.max-h-\[270px\] {
  max-height: 270px;
}

.max-h-\[428px\] {
  max-height: 428px;
}

.max-h-full {
  max-height: 100%;
}

.min-h-80 {
  min-height: 20rem;
}

.min-h-\[428px\] {
  min-height: 428px;
}

.min-h-\[520px\] {
  min-height: 520px;
}

.min-h-\[530px\] {
  min-height: 530px;
}

.min-h-\[90px\] {
  min-height: 90px;
}

.\!w-52 {
  width: 13rem !important;
}

.\!w-full {
  width: 100% !important;
}

.w-10 {
  width: 2.5rem;
}

.w-12 {
  width: 3rem;
}

.w-16 {
  width: 4rem;
}

.w-20 {
  width: 5rem;
}

.w-24 {
  width: 6rem;
}

.w-4 {
  width: 1rem;
}

.w-5 {
  width: 1.25rem;
}

.w-6 {
  width: 1.5rem;
}

.w-60 {
  width: 15rem;
}

.w-7 {
  width: 1.75rem;
}

.w-8 {
  width: 2rem;
}

.w-9 {
  width: 2.25rem;
}

.w-\[125\%\] {
  width: 125%;
}

.w-\[180px\] {
  width: 180px;
}

.w-\[206px\] {
  width: 206px;
}

.w-\[20px\] {
  width: 20px;
}

.w-\[22px\] {
  width: 22px;
}

.w-\[260px\] {
  width: 260px;
}

.w-\[40\%\] {
  width: 40%;
}

.w-\[430px\] {
  width: 430px;
}

.w-\[432px\] {
  width: 432px;
}

.w-\[50\%\] {
  width: 50%;
}

.w-\[60px\] {
  width: 60px;
}

.w-\[75px\] {
  width: 75px;
}

.w-\[90\%\] {
  width: 90%;
}

.w-\[90px\] {
  width: 90px;
}

.w-\[92\%\] {
  width: 92%;
}

.w-\[94\%\] {
  width: 94%;
}

.w-\[95\%\] {
  width: 95%;
}

.w-auto {
  width: auto;
}

.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}

.w-full {
  width: 100%;
}

.min-w-14 {
  min-width: 3.5rem;
}

.min-w-\[200px\] {
  min-width: 200px;
}

.min-w-\[380px\] {
  min-width: 380px;
}

.min-w-\[85\%\] {
  min-width: 85%;
}

.min-w-\[92\%\] {
  min-width: 92%;
}

.min-w-fit {
  min-width: -moz-fit-content;
  min-width: fit-content;
}

.min-w-max {
  min-width: -moz-max-content;
  min-width: max-content;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-52 {
  max-width: 13rem;
}

.max-w-\[150px\] {
  max-width: 150px;
}

.max-w-\[180px\] {
  max-width: 180px;
}

.max-w-\[280px\] {
  max-width: 280px;
}

.max-w-\[468px\] {
  max-width: 468px;
}

.max-w-\[520px\] {
  max-width: 520px;
}

.max-w-\[560px\] {
  max-width: 560px;
}

.max-w-\[580px\] {
  max-width: 580px;
}

.max-w-\[600px\] {
  max-width: 600px;
}

.max-w-\[765px\] {
  max-width: 765px;
}

.max-w-\[800px\] {
  max-width: 800px;
}

.max-w-\[85\%\] {
  max-width: 85%;
}

.max-w-\[850px\] {
  max-width: 850px;
}

.max-w-\[8rem\] {
  max-width: 8rem;
}

.max-w-full {
  max-width: 100%;
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-none {
  max-width: none;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-xl {
  max-width: 36rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-none {
  flex: none;
}

.flex-shrink {
  flex-shrink: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink-0 {
  flex-shrink: 0;
}

.grow {
  flex-grow: 1;
}

.grow-0 {
  flex-grow: 0;
}

.table-auto {
  table-layout: auto;
}

.border-separate {
  border-collapse: separate;
}

.border-spacing-0 {
  --tw-border-spacing-x: 0px;
  --tw-border-spacing-y: 0px;
  border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-full {
  --tw-translate-y: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0\.5 {
  --tw-translate-y: 0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-full {
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform-none {
  transform: none;
}

@keyframes infinite-scroll {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(-100%);
  }
}

.animate-infinite-scroll {
  animation: infinite-scroll 25s linear infinite;
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.cursor-default {
  cursor: default;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.resize {
  resize: both;
}

.snap-x {
  scroll-snap-type: x var(--tw-scroll-snap-strictness);
}

.snap-mandatory {
  --tw-scroll-snap-strictness: mandatory;
}

.snap-start {
  scroll-snap-align: start;
}

.snap-always {
  scroll-snap-stop: always;
}

.list-inside {
  list-style-position: inside;
}

.list-disc {
  list-style-type: disc;
}

.list-none {
  list-style-type: none;
}

.grid-flow-row {
  grid-auto-flow: row;
}

.auto-rows-max {
  grid-auto-rows: max-content;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.content-between {
  align-content: space-between;
}

.content-stretch {
  align-content: stretch;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

.justify-normal {
  justify-content: normal;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-evenly {
  justify-content: space-evenly;
}

.justify-stretch {
  justify-content: stretch;
}

.justify-items-end {
  justify-items: end;
}

.gap-0 {
  gap: 0px;
}

.gap-0\.5 {
  gap: 0.125rem;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-1\.5 {
  gap: 0.375rem;
}

.gap-10 {
  gap: 2.5rem;
}

.gap-12 {
  gap: 3rem;
}

.gap-14 {
  gap: 3.5rem;
}

.gap-16 {
  gap: 4rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-2\.5 {
  gap: 0.625rem;
}

.gap-20 {
  gap: 5rem;
}

.gap-24 {
  gap: 6rem;
}

.gap-28 {
  gap: 7rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-32 {
  gap: 8rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-7 {
  gap: 1.75rem;
}

.gap-72 {
  gap: 18rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-y-3 {
  row-gap: 0.75rem;
}

.space-y-10 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-gray-2 > :not([hidden]) ~ :not([hidden]) {
  border-color: var(--color-gray-2);
}

.divide-neutral-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-divide-opacity));
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.text-ellipsis {
  text-overflow: ellipsis;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-3xl {
  border-radius: 1.5rem;
}

.rounded-\[50px\] {
  border-radius: 50px;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-none {
  border-radius: 0px;
}

.rounded-sm {
  border-radius: 0.125rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-b-2xl {
  border-bottom-right-radius: 1rem;
  border-bottom-left-radius: 1rem;
}

.rounded-b-xl {
  border-bottom-right-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}

.rounded-t-xl {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.rounded-tl-2xl {
  border-top-left-radius: 1rem;
}

.rounded-tr-2xl {
  border-top-right-radius: 1rem;
}

.rounded-tr-full {
  border-top-right-radius: 9999px;
}

.border {
  border-width: 1px;
}

.border-2 {
  border-width: 2px;
}

.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-0 {
  border-bottom-width: 0px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-t {
  border-top-width: 1px;
}

.border-t-0 {
  border-top-width: 0px;
}

.border-none {
  border-style: none;
}

.\!border-\[\#C8277D\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(200 39 125 / var(--tw-border-opacity)) !important;
}

.border-\[\#C8277D\] {
  --tw-border-opacity: 1;
  border-color: rgb(200 39 125 / var(--tw-border-opacity));
}

.border-\[\#EB3B72\] {
  --tw-border-opacity: 1;
  border-color: rgb(235 59 114 / var(--tw-border-opacity));
}

.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity));
}

.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity));
}

.border-gray-2 {
  border-color: var(--color-gray-2);
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}

.border-gray-7 {
  border-color: var(--color-gray-7);
}

.border-primary {
  border-color: var(--color-primary);
}

.bg-\[\#1B2842\] {
  --tw-bg-opacity: 1;
  background-color: rgb(27 40 66 / var(--tw-bg-opacity));
}

.bg-\[\#53cc2824\] {
  background-color: #53cc2824;
}

.bg-\[\#81567E\] {
  --tw-bg-opacity: 1;
  background-color: rgb(129 86 126 / var(--tw-bg-opacity));
}

.bg-\[\#C2D3D2\] {
  --tw-bg-opacity: 1;
  background-color: rgb(194 211 210 / var(--tw-bg-opacity));
}

.bg-\[\#DBC9DA\] {
  --tw-bg-opacity: 1;
  background-color: rgb(219 201 218 / var(--tw-bg-opacity));
}

.bg-\[\#E6E6E6\] {
  --tw-bg-opacity: 1;
  background-color: rgb(230 230 230 / var(--tw-bg-opacity));
}

.bg-\[\#EBEBEB\] {
  --tw-bg-opacity: 1;
  background-color: rgb(235 235 235 / var(--tw-bg-opacity));
}

.bg-\[\#F3F3F3\] {
  --tw-bg-opacity: 1;
  background-color: rgb(243 243 243 / var(--tw-bg-opacity));
}

.bg-\[\#F5F5F5\] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity));
}

.bg-\[\#fff6f8\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 246 248 / var(--tw-bg-opacity));
}

.bg-black {
  background-color: var(--color-black);
}

.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity));
}

.bg-gray-1 {
  background-color: var(--color-gray-1);
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.bg-gray-2 {
  background-color: var(--color-gray-2);
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.bg-gray-3 {
  background-color: var(--color-gray-3);
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.bg-gray-900\/50 {
  background-color: rgb(17 24 39 / 0.5);
}

.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity));
}

.bg-primary {
  background-color: var(--color-primary);
}

.bg-primary-light {
  background-color: var(--color-primary-light);
}

.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity));
}

.bg-secondary {
  background-color: var(--color-secondary);
}

.bg-slate-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity));
}

.bg-transparent {
  background-color: transparent;
}

.bg-white {
  background-color: var(--color-white);
}

.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity));
}

.bg-opacity-35 {
  --tw-bg-opacity: 0.35;
}

.bg-gradient-primary {
  background-image: var(--gradient-primary);
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-\[\#C8277D\] {
  --tw-gradient-from: #C8277D var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(200 39 125 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-\[\#D22725\] {
  --tw-gradient-from: #D22725 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(210 39 37 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-white {
  --tw-gradient-from: var(--color-white) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-white {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--color-white) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.to-\[\#C8277D\] {
  --tw-gradient-to: #C8277D var(--tw-gradient-to-position);
}

.to-\[\#D22725\] {
  --tw-gradient-to: #D22725 var(--tw-gradient-to-position);
}

.fill-\[\#D22725\] {
  fill: #D22725;
}

.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.object-top {
  -o-object-position: top;
     object-position: top;
}

.p-1 {
  padding: 0.25rem;
}

.p-1\.5 {
  padding: 0.375rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-2\.5 {
  padding: 0.625rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-9 {
  padding: 2.25rem;
}

.p-\[26px\] {
  padding: 26px;
}

.p-\[2px\] {
  padding: 2px;
}

.\!px-12 {
  padding-left: 3rem !important;
  padding-right: 3rem !important;
}

.\!px-16 {
  padding-left: 4rem !important;
  padding-right: 4rem !important;
}

.\!px-24 {
  padding-left: 6rem !important;
  padding-right: 6rem !important;
}

.\!px-3 {
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
}

.\!px-6 {
  padding-left: 1.5rem !important;
  padding-right: 1.5rem !important;
}

.\!px-8 {
  padding-left: 2rem !important;
  padding-right: 2rem !important;
}

.\!px-9 {
  padding-left: 2.25rem !important;
  padding-right: 2.25rem !important;
}

.\!py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.\!py-2\.5 {
  padding-top: 0.625rem !important;
  padding-bottom: 0.625rem !important;
}

.\!py-3 {
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-12 {
  padding-left: 3rem;
  padding-right: 3rem;
}

.px-14 {
  padding-left: 3.5rem;
  padding-right: 3.5rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-3\.5 {
  padding-left: 0.875rem;
  padding-right: 0.875rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.px-\[9px\] {
  padding-left: 9px;
  padding-right: 9px;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-14 {
  padding-top: 3.5rem;
  padding-bottom: 3.5rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-7 {
  padding-top: 1.75rem;
  padding-bottom: 1.75rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-9 {
  padding-top: 2.25rem;
  padding-bottom: 2.25rem;
}

.py-\[10px\] {
  padding-top: 10px;
  padding-bottom: 10px;
}

.py-\[18px\] {
  padding-top: 18px;
  padding-bottom: 18px;
}

.py-\[2px\] {
  padding-top: 2px;
  padding-bottom: 2px;
}

.py-\[30px\] {
  padding-top: 30px;
  padding-bottom: 30px;
}

.py-\[6px\] {
  padding-top: 6px;
  padding-bottom: 6px;
}

.py-\[7px\] {
  padding-top: 7px;
  padding-bottom: 7px;
}

.\!pl-16 {
  padding-left: 4rem !important;
}

.pb-0 {
  padding-bottom: 0px;
}

.pb-1 {
  padding-bottom: 0.25rem;
}

.pb-10 {
  padding-bottom: 2.5rem;
}

.pb-12 {
  padding-bottom: 3rem;
}

.pb-14 {
  padding-bottom: 3.5rem;
}

.pb-16 {
  padding-bottom: 4rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-20 {
  padding-bottom: 5rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-5 {
  padding-bottom: 1.25rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pb-8 {
  padding-bottom: 2rem;
}

.pl-5 {
  padding-left: 1.25rem;
}

.pl-7 {
  padding-left: 1.75rem;
}

.pr-1 {
  padding-right: 0.25rem;
}

.pr-1\.5 {
  padding-right: 0.375rem;
}

.pr-\[7px\] {
  padding-right: 7px;
}

.ps-11 {
  padding-inline-start: 2.75rem;
}

.ps-3 {
  padding-inline-start: 0.75rem;
}

.ps-4 {
  padding-inline-start: 1rem;
}

.pt-0 {
  padding-top: 0px;
}

.pt-10 {
  padding-top: 2.5rem;
}

.pt-12 {
  padding-top: 3rem;
}

.pt-14 {
  padding-top: 3.5rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-7 {
  padding-top: 1.75rem;
}

.pt-8 {
  padding-top: 2rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-justify {
  text-align: justify;
}

.align-middle {
  vertical-align: middle;
}

.\!text-base {
  font-size: 1rem !important;
  line-height: 1.5rem !important;
}

.\!text-sm {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}

.text-\[11px\] {
  font-size: 11px;
}

.text-\[12px\] {
  font-size: 12px;
}

.text-\[13px\] {
  font-size: 13px;
}

.text-\[22px\] {
  font-size: 22px;
}

.text-\[24px\] {
  font-size: 24px;
}

.text-\[28px\] {
  font-size: 28px;
}

.text-\[34px\] {
  font-size: 34px;
}

.text-\[40px\] {
  font-size: 40px;
}

.text-\[48px\] {
  font-size: 48px;
}

.text-\[52px\] {
  font-size: 52px;
}

.text-\[64px\] {
  font-size: 64px;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.\!font-bold {
  font-weight: 700 !important;
}

.\!font-semibold {
  font-weight: 600 !important;
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.\!leading-normal {
  line-height: 1.5 !important;
}

.leading-3 {
  line-height: .75rem;
}

.leading-4 {
  line-height: 1rem;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-7 {
  line-height: 1.75rem;
}

.leading-8 {
  line-height: 2rem;
}

.leading-9 {
  line-height: 2.25rem;
}

.leading-\[0\] {
  line-height: 0;
}

.leading-\[0px\] {
  line-height: 0px;
}

.leading-\[1\.1\] {
  line-height: 1.1;
}

.leading-\[16px\] {
  line-height: 16px;
}

.leading-\[22px\] {
  line-height: 22px;
}

.leading-\[38px\] {
  line-height: 38px;
}

.leading-\[54px\] {
  line-height: 54px;
}

.leading-\[68px\] {
  line-height: 68px;
}

.leading-none {
  line-height: 1;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.\!text-gray-3 {
  color: var(--color-gray-3) !important;
}

.\!text-gray-8 {
  color: var(--color-gray-8) !important;
}

.\!text-white {
  color: var(--color-white) !important;
}

.text-\[\#101828\] {
  --tw-text-opacity: 1;
  color: rgb(16 24 40 / var(--tw-text-opacity));
}

.text-\[\#4C6A68\] {
  --tw-text-opacity: 1;
  color: rgb(76 106 104 / var(--tw-text-opacity));
}

.text-\[\#53CC28\] {
  --tw-text-opacity: 1;
  color: rgb(83 204 40 / var(--tw-text-opacity));
}

.text-\[\#C8277D\] {
  --tw-text-opacity: 1;
  color: rgb(200 39 125 / var(--tw-text-opacity));
}

.text-\[\#D22721\] {
  --tw-text-opacity: 1;
  color: rgb(210 39 33 / var(--tw-text-opacity));
}

.text-\[\#DBC9DA\] {
  --tw-text-opacity: 1;
  color: rgb(219 201 218 / var(--tw-text-opacity));
}

.text-\[\#EB3B72\] {
  --tw-text-opacity: 1;
  color: rgb(235 59 114 / var(--tw-text-opacity));
}

.text-\[\#d7d7d7\] {
  --tw-text-opacity: 1;
  color: rgb(215 215 215 / var(--tw-text-opacity));
}

.text-black {
  color: var(--color-black);
}

.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity));
}

.text-gray-2 {
  color: var(--color-gray-2);
}

.text-gray-3 {
  color: var(--color-gray-3);
}

.text-gray-4 {
  color: var(--color-gray-4);
}

.text-gray-5 {
  color: var(--color-gray-5);
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.text-gray-6 {
  color: var(--color-gray-6);
}

.text-gray-7 {
  color: var(--color-gray-7);
}

.text-gray-8 {
  color: var(--color-gray-8);
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.text-gray-9 {
  color: var(--color-gray-9);
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity));
}

.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity));
}

.text-primary-heather_blush {
  color: var(--color-primary-heather-blush);
}

.text-primary-muted_seafoam {
  color: var(--color-primary-muted-seafoam);
}

.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity));
}

.text-secondary {
  color: var(--color-secondary);
}

.text-white {
  color: var(--color-white);
}

.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity));
}

.underline {
  text-decoration-line: underline;
}

.line-through {
  text-decoration-line: line-through;
}

.placeholder-gray-4::-moz-placeholder {
  color: var(--color-gray-4);
}

.placeholder-gray-4::placeholder {
  color: var(--color-gray-4);
}

.placeholder-gray-5::-moz-placeholder {
  color: var(--color-gray-5);
}

.placeholder-gray-5::placeholder {
  color: var(--color-gray-5);
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-20 {
  opacity: 0.2;
}

.opacity-40 {
  opacity: 0.4;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-55 {
  opacity: 0.55;
}

.opacity-70 {
  opacity: 0.7;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline {
  outline-style: solid;
}

.outline-secondary {
  outline-color: var(--color-secondary);
}

.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-gray-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity));
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.text-primary-gradient {
  background-image: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.paragraph-text {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  color: var(--color-gray-8);
}

.paragraph-text-responsive {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 400;
  color: var(--color-gray-8);
}

@media (min-width: 768px) {
  .paragraph-text-responsive {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.button-primary-gradient {
  border-radius: 9999px;
  background-image: var(--gradient-primary);
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 6px;
  padding-bottom: 6px;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  line-height: 22px;
  color: var(--color-white);
}

.button-secondary {
  height: 2.25rem;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(235 235 235 / var(--tw-bg-opacity));
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 6px;
  padding-bottom: 6px;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  line-height: 22px;
  color: var(--color-gray-8);
}

.button-primary-light {
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(245 212 221 / var(--tw-bg-opacity));
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 6px;
  padding-bottom: 6px;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 700;
}

.input-field {
  display: block;
  width: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  border-color: var(--color-gray-2);
  background-color: var(--color-white);
  padding: 0.625rem;
  padding-left: 0.875rem;
  padding-right: 0.875rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  color: var(--color-gray-8);
}

.input-field:focus {
  border-color: var(--color-gray-2);
  outline: 2px solid transparent;
  outline-offset: 2px;
}

/* Defined the heading Level */

.heading-level-1 {
  font-size: 28px;
  font-weight: 700;
  line-height: 1.25;
  color: var(--color-secondary);
}

@media (min-width: 640px) {
  .heading-level-1 {
    font-size: 36px;
    line-height: 44px;
  }
}

@media (min-width: 768px) {
  .heading-level-1 {
    font-size: 40px;
    line-height: 48px;
  }
}

.heading-level-2 {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  color: var(--color-secondary);
}

@media (min-width: 640px) {
  .heading-level-2 {
    font-size: 28px;
    line-height: 38px;
  }
}

@media (min-width: 768px) {
  .heading-level-2 {
    font-size: 36px;
    line-height: 44px;
  }
}

.heading-level-3 {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  color: var(--color-secondary);
}

@media (min-width: 768px) {
  .heading-level-3 {
    font-size: 28px;
    line-height: 2.25rem;
  }
}

.heading-level-4 {
  font-size: 22px;
  font-weight: 700;
  line-height: 1.75rem;
  color: var(--color-secondary);
}

.section-heading-40 {
  font-size: 28px;
  font-weight: 700;
  line-height: 1.25;
}

@media (min-width: 768px) {
  .section-heading-40 {
    font-size: 40px;
    line-height: 48px;
  }
}

@media (min-width: 1024px) {
  .lg\:container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
  }

  @media (min-width: 640px) {
    .lg\:container {
      max-width: 100%;
    }
  }

  @media (min-width: 768px) {
    .lg\:container {
      max-width: 100%;
    }
  }

  @media (min-width: 1024px) {
    .lg\:container {
      max-width: 1024px;
    }
  }

  @media (min-width: 1280px) {
    .lg\:container {
      max-width: 1216px;
    }
  }
}

.last\:border-none:last-child {
  border-style: none;
}

.hover\:scale-125:hover {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.hover\:bg-\[\#fafafaea\]:hover {
  background-color: #fafafaea;
}

.hover\:bg-gray-2:hover {
  background-color: var(--color-gray-2);
}

.hover\:bg-gray-4:hover {
  background-color: var(--color-gray-4);
}

.hover\:bg-white:hover {
  background-color: var(--color-white);
}

.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.hover\:text-primary:hover {
  color: var(--color-primary);
}

.focus\:border-gray-2:focus {
  border-color: var(--color-gray-2);
}

.focus\:border-primary:focus {
  border-color: var(--color-primary);
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-gray-2:focus {
  --tw-ring-color: var(--color-gray-2);
}

.focus-visible\:shadow-none:focus-visible {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[open] .group-open\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:text-primary {
  color: var(--color-primary);
}

.peer:checked ~ .peer-checked\:bg-\[\#1B2842\] {
  --tw-bg-opacity: 1;
  background-color: rgb(27 40 66 / var(--tw-bg-opacity));
}

.peer:checked ~ .peer-checked\:bg-white {
  background-color: var(--color-white);
}

.peer:checked ~ .peer-checked\:text-\[\#101828\] {
  --tw-text-opacity: 1;
  color: rgb(16 24 40 / var(--tw-text-opacity));
}

.peer:checked ~ .peer-checked\:text-primary {
  color: var(--color-primary);
}

.peer:checked ~ .peer-checked\:text-white {
  color: var(--color-white);
}

@media (min-width: 640px) {
  .sm\:absolute {
    position: absolute;
  }

  .sm\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .sm\:mb-0 {
    margin-bottom: 0px;
  }
}

@media (min-width: 768px) {
  .md\:invisible {
    visibility: hidden;
  }

  .md\:absolute {
    position: absolute;
  }

  .md\:sticky {
    position: sticky;
  }

  .md\:inset-0 {
    inset: 0px;
  }

  .md\:-right-0 {
    right: -0px;
  }

  .md\:-right-40 {
    right: -10rem;
  }

  .md\:-top-16 {
    top: -4rem;
  }

  .md\:left-\[40\%\] {
    left: 40%;
  }

  .md\:top-0 {
    top: 0px;
  }

  .md\:top-16 {
    top: 4rem;
  }

  .md\:top-full {
    top: 100%;
  }

  .md\:z-\[1\] {
    z-index: 1;
  }

  .md\:order-first {
    order: -9999;
  }

  .md\:order-none {
    order: 0;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:mx-8 {
    margin-left: 2rem;
    margin-right: 2rem;
  }

  .md\:my-12 {
    margin-top: 3rem;
    margin-bottom: 3rem;
  }

  .md\:-mt-12 {
    margin-top: -3rem;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mb-1 {
    margin-bottom: 0.25rem;
  }

  .md\:mb-1\.5 {
    margin-bottom: 0.375rem;
  }

  .md\:mb-10 {
    margin-bottom: 2.5rem;
  }

  .md\:mb-11 {
    margin-bottom: 2.75rem;
  }

  .md\:mb-12 {
    margin-bottom: 3rem;
  }

  .md\:mb-16 {
    margin-bottom: 4rem;
  }

  .md\:mb-4 {
    margin-bottom: 1rem;
  }

  .md\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .md\:mb-8 {
    margin-bottom: 2rem;
  }

  .md\:mb-9 {
    margin-bottom: 2.25rem;
  }

  .md\:mb-\[52px\] {
    margin-bottom: 52px;
  }

  .md\:ml-\[35\%\] {
    margin-left: 35%;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-10 {
    margin-top: 2.5rem;
  }

  .md\:mt-11 {
    margin-top: 2.75rem;
  }

  .md\:mt-12 {
    margin-top: 3rem;
  }

  .md\:mt-14 {
    margin-top: 3.5rem;
  }

  .md\:mt-16 {
    margin-top: 4rem;
  }

  .md\:mt-2 {
    margin-top: 0.5rem;
  }

  .md\:mt-20 {
    margin-top: 5rem;
  }

  .md\:mt-24 {
    margin-top: 6rem;
  }

  .md\:mt-28 {
    margin-top: 7rem;
  }

  .md\:mt-3 {
    margin-top: 0.75rem;
  }

  .md\:mt-6 {
    margin-top: 1.5rem;
  }

  .md\:mt-7 {
    margin-top: 1.75rem;
  }

  .md\:mt-8 {
    margin-top: 2rem;
  }

  .md\:mt-9 {
    margin-top: 2.25rem;
  }

  .md\:mt-\[60px\] {
    margin-top: 60px;
  }

  .md\:block {
    display: block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-\[365px\] {
    height: 365px;
  }

  .md\:h-\[450px\] {
    height: 450px;
  }

  .md\:h-\[749px\] {
    height: 749px;
  }

  .md\:h-\[calc\(100vh-92px\)\] {
    height: calc(100vh - 92px);
  }

  .md\:h-full {
    height: 100%;
  }

  .md\:max-h-24 {
    max-height: 6rem;
  }

  .md\:max-h-\[428px\] {
    max-height: 428px;
  }

  .md\:max-h-\[600px\] {
    max-height: 600px;
  }

  .md\:min-h-\[428px\] {
    min-height: 428px;
  }

  .md\:min-h-\[485px\] {
    min-height: 485px;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-1\/3 {
    width: 33.333333%;
  }

  .md\:w-1\/4 {
    width: 25%;
  }

  .md\:w-1\/5 {
    width: 20%;
  }

  .md\:w-2\/3 {
    width: 66.666667%;
  }

  .md\:w-2\/4 {
    width: 50%;
  }

  .md\:w-2\/5 {
    width: 40%;
  }

  .md\:w-3\/4 {
    width: 75%;
  }

  .md\:w-3\/5 {
    width: 60%;
  }

  .md\:w-4\/5 {
    width: 80%;
  }

  .md\:w-44 {
    width: 11rem;
  }

  .md\:w-60 {
    width: 15rem;
  }

  .md\:w-96 {
    width: 24rem;
  }

  .md\:w-\[350px\] {
    width: 350px;
  }

  .md\:w-\[354px\] {
    width: 354px;
  }

  .md\:w-\[50\%\] {
    width: 50%;
  }

  .md\:w-\[55\%\] {
    width: 55%;
  }

  .md\:w-\[572px\] {
    width: 572px;
  }

  .md\:w-\[65\%\] {
    width: 65%;
  }

  .md\:w-\[650px\] {
    width: 650px;
  }

  .md\:w-\[70\%\] {
    width: 70%;
  }

  .md\:w-\[74\%\] {
    width: 74%;
  }

  .md\:w-\[78\%\] {
    width: 78%;
  }

  .md\:w-\[780px\] {
    width: 780px;
  }

  .md\:w-\[80\%\] {
    width: 80%;
  }

  .md\:w-\[85\%\] {
    width: 85%;
  }

  .md\:w-\[9\.5rem\] {
    width: 9.5rem;
  }

  .md\:w-\[90\%\] {
    width: 90%;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:min-w-\[448px\] {
    min-width: 448px;
  }

  .md\:max-w-2xl {
    max-width: 42rem;
  }

  .md\:max-w-3xl {
    max-width: 48rem;
  }

  .md\:max-w-4xl {
    max-width: 56rem;
  }

  .md\:max-w-5xl {
    max-width: 64rem;
  }

  .md\:max-w-6xl {
    max-width: 72rem;
  }

  .md\:max-w-7xl {
    max-width: 80rem;
  }

  .md\:max-w-\[345px\] {
    max-width: 345px;
  }

  .md\:max-w-\[354px\] {
    max-width: 354px;
  }

  .md\:max-w-\[42rem\] {
    max-width: 42rem;
  }

  .md\:max-w-\[480px\] {
    max-width: 480px;
  }

  .md\:max-w-\[590px\] {
    max-width: 590px;
  }

  .md\:max-w-full {
    max-width: 100%;
  }

  .md\:max-w-lg {
    max-width: 32rem;
  }

  .md\:max-w-md {
    max-width: 28rem;
  }

  .md\:max-w-none {
    max-width: none;
  }

  .md\:max-w-sm {
    max-width: 24rem;
  }

  .md\:max-w-xl {
    max-width: 36rem;
  }

  .md\:max-w-xs {
    max-width: 20rem;
  }

  .md\:flex-none {
    flex: none;
  }

  .md\:rotate-0 {
    --tw-rotate: 0deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:snap-none {
    scroll-snap-type: none;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-row-reverse {
    flex-direction: row-reverse;
  }

  .md\:items-end {
    align-items: flex-end;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-normal {
    justify-content: normal;
  }

  .md\:justify-start {
    justify-content: flex-start;
  }

  .md\:justify-end {
    justify-content: flex-end;
  }

  .md\:justify-center {
    justify-content: center;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:justify-stretch {
    justify-content: stretch;
  }

  .md\:gap-0 {
    gap: 0px;
  }

  .md\:gap-10 {
    gap: 2.5rem;
  }

  .md\:gap-12 {
    gap: 3rem;
  }

  .md\:gap-14 {
    gap: 3.5rem;
  }

  .md\:gap-16 {
    gap: 4rem;
  }

  .md\:gap-2 {
    gap: 0.5rem;
  }

  .md\:gap-20 {
    gap: 5rem;
  }

  .md\:gap-24 {
    gap: 6rem;
  }

  .md\:gap-28 {
    gap: 7rem;
  }

  .md\:gap-3 {
    gap: 0.75rem;
  }

  .md\:gap-32 {
    gap: 8rem;
  }

  .md\:gap-36 {
    gap: 9rem;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:gap-6 {
    gap: 1.5rem;
  }

  .md\:gap-8 {
    gap: 2rem;
  }

  .md\:space-y-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
  }

  .md\:overflow-y-auto {
    overflow-y: auto;
  }

  .md\:rounded-tr-2xl {
    border-top-right-radius: 1rem;
  }

  .md\:rounded-tr-none {
    border-top-right-radius: 0px;
  }

  .md\:border {
    border-width: 1px;
  }

  .md\:border-gray-2 {
    border-color: var(--color-gray-2);
  }

  .md\:bg-gray-2 {
    background-color: var(--color-gray-2);
  }

  .md\:p-0 {
    padding: 0px;
  }

  .md\:p-16 {
    padding: 4rem;
  }

  .md\:p-4 {
    padding: 1rem;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:p-9 {
    padding: 2.25rem;
  }

  .md\:\!px-20 {
    padding-left: 5rem !important;
    padding-right: 5rem !important;
  }

  .md\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .md\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .md\:px-14 {
    padding-left: 3.5rem;
    padding-right: 3.5rem;
  }

  .md\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .md\:px-20 {
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .md\:px-24 {
    padding-left: 6rem;
    padding-right: 6rem;
  }

  .md\:px-28 {
    padding-left: 7rem;
    padding-right: 7rem;
  }

  .md\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:px-7 {
    padding-left: 1.75rem;
    padding-right: 1.75rem;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .md\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .md\:py-11 {
    padding-top: 2.75rem;
    padding-bottom: 2.75rem;
  }

  .md\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .md\:py-14 {
    padding-top: 3.5rem;
    padding-bottom: 3.5rem;
  }

  .md\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .md\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .md\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .md\:py-28 {
    padding-top: 7rem;
    padding-bottom: 7rem;
  }

  .md\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .md\:py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
  }

  .md\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .md\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .md\:pb-0 {
    padding-bottom: 0px;
  }

  .md\:pb-10 {
    padding-bottom: 2.5rem;
  }

  .md\:pb-12 {
    padding-bottom: 3rem;
  }

  .md\:pb-14 {
    padding-bottom: 3.5rem;
  }

  .md\:pb-16 {
    padding-bottom: 4rem;
  }

  .md\:pb-20 {
    padding-bottom: 5rem;
  }

  .md\:pb-24 {
    padding-bottom: 6rem;
  }

  .md\:pb-28 {
    padding-bottom: 7rem;
  }

  .md\:pb-32 {
    padding-bottom: 8rem;
  }

  .md\:pb-4 {
    padding-bottom: 1rem;
  }

  .md\:pb-6 {
    padding-bottom: 1.5rem;
  }

  .md\:pb-8 {
    padding-bottom: 2rem;
  }

  .md\:pl-0 {
    padding-left: 0px;
  }

  .md\:pl-16 {
    padding-left: 4rem;
  }

  .md\:pl-64 {
    padding-left: 16rem;
  }

  .md\:pr-1 {
    padding-right: 0.25rem;
  }

  .md\:pt-0 {
    padding-top: 0px;
  }

  .md\:pt-10 {
    padding-top: 2.5rem;
  }

  .md\:pt-12 {
    padding-top: 3rem;
  }

  .md\:pt-14 {
    padding-top: 3.5rem;
  }

  .md\:pt-16 {
    padding-top: 4rem;
  }

  .md\:pt-20 {
    padding-top: 5rem;
  }

  .md\:pt-4 {
    padding-top: 1rem;
  }

  .md\:pt-6 {
    padding-top: 1.5rem;
  }

  .md\:pt-8 {
    padding-top: 2rem;
  }

  .md\:text-left {
    text-align: left;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:text-\[28px\] {
    font-size: 28px;
  }

  .md\:text-\[34px\] {
    font-size: 34px;
  }

  .md\:text-\[40px\] {
    font-size: 40px;
  }

  .md\:text-\[52px\] {
    font-size: 52px;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .md\:leading-10 {
    line-height: 2.5rem;
  }

  .md\:leading-9 {
    line-height: 2.25rem;
  }

  .md\:leading-\[46px\] {
    line-height: 46px;
  }

  .md\:leading-\[60px\] {
    line-height: 60px;
  }

  .md\:leading-none {
    line-height: 1;
  }

  .md\:opacity-0 {
    opacity: 0;
  }

  .md\:shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}

@media (min-width: 1024px) {
  .lg\:static {
    position: static;
  }

  .lg\:my-0 {
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .lg\:my-auto {
    margin-top: auto;
    margin-bottom: auto;
  }

  .lg\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .lg\:mr-0 {
    margin-right: 0px;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:inline-flex {
    display: inline-flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-auto {
    height: auto;
  }

  .lg\:min-h-fit {
    min-height: -moz-fit-content;
    min-height: fit-content;
  }

  .lg\:w-\[46\%\] {
    width: 46%;
  }

  .lg\:w-\[462px\] {
    width: 462px;
  }

  .lg\:w-\[528px\] {
    width: 528px;
  }

  .lg\:w-\[540px\] {
    width: 540px;
  }

  .lg\:w-\[590px\] {
    width: 590px;
  }

  .lg\:w-full {
    width: 100%;
  }

  .lg\:max-w-\[350px\] {
    max-width: 350px;
  }

  .lg\:max-w-\[462px\] {
    max-width: 462px;
  }

  .lg\:max-w-\[540px\] {
    max-width: 540px;
  }

  .lg\:max-w-full {
    max-width: 100%;
  }

  .lg\:max-w-lg {
    max-width: 32rem;
  }

  .lg\:max-w-screen-xl {
    max-width: 1280px;
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:content-normal {
    align-content: normal;
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:justify-end {
    justify-content: flex-end;
  }

  .lg\:rounded-none {
    border-radius: 0px;
  }

  .lg\:bg-transparent {
    background-color: transparent;
  }

  .lg\:p-0 {
    padding: 0px;
  }

  .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .lg\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .lg\:pt-11 {
    padding-top: 2.75rem;
  }

  .lg\:text-left {
    text-align: left;
  }

  .lg\:text-9xl {
    font-size: 8rem;
    line-height: 1;
  }

  .lg\:text-\[40px\] {
    font-size: 40px;
  }

  .lg\:text-\[52px\] {
    font-size: 52px;
  }

  .lg\:text-\[64px\] {
    font-size: 64px;
  }

  .lg\:leading-\[1\.1\] {
    line-height: 1.1;
  }

  .lg\:leading-\[70px\] {
    line-height: 70px;
  }

  .lg\:shadow-none {
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}

@media (min-width: 1280px) {
  .xl\:mr-24 {
    margin-right: 6rem;
  }
}

@media (prefers-color-scheme: dark) {
  .dark\:border-blue-500 {
    --tw-border-opacity: 1;
    border-color: rgb(59 130 246 / var(--tw-border-opacity));
  }

  .dark\:border-gray-700 {
    --tw-border-opacity: 1;
    border-color: rgb(55 65 81 / var(--tw-border-opacity));
  }

  .dark\:border-transparent {
    border-color: transparent;
  }

  .dark\:bg-gray-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55 / var(--tw-bg-opacity));
  }

  .dark\:bg-gray-800\/50 {
    background-color: rgb(31 41 55 / 0.5);
  }

  .dark\:bg-gray-900\/80 {
    background-color: rgb(17 24 39 / 0.8);
  }

  .dark\:text-blue-500 {
    --tw-text-opacity: 1;
    color: rgb(59 130 246 / var(--tw-text-opacity));
  }

  .dark\:text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity));
  }

  .dark\:text-white {
    color: var(--color-white);
  }

  .dark\:hover\:bg-gray-800:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55 / var(--tw-bg-opacity));
  }

  .dark\:hover\:text-blue-500:hover {
    --tw-text-opacity: 1;
    color: rgb(59 130 246 / var(--tw-text-opacity));
  }

  .dark\:hover\:text-gray-300:hover {
    --tw-text-opacity: 1;
    color: rgb(209 213 219 / var(--tw-text-opacity));
  }
}

.\[\&_div\]\:space-y-5 div > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
